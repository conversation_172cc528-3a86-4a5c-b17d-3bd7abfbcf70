#!/usr/bin/env python3
"""
Quick connection test to diagnose WordPress-Flask connectivity issues
"""

import requests
import json

def test_flask_connection():
    """Test Flask API connectivity"""
    print("🔍 Testing Flask API Connection...")
    print("=" * 50)
    
    urls_to_test = [
        "http://localhost:5000",
        "http://127.0.0.1:5000"
    ]
    
    for base_url in urls_to_test:
        print(f"\n📍 Testing: {base_url}")
        
        # Test health endpoint
        try:
            response = requests.get(f"{base_url}/health", timeout=5)
            print(f"   Health Check: ✅ Status {response.status_code}")
            print(f"   Response: {response.text}")
        except requests.exceptions.ConnectionError:
            print(f"   Health Check: ❌ Connection refused")
            continue
        except Exception as e:
            print(f"   Health Check: ❌ Error: {e}")
            continue
        
        # Test upload endpoint (simulate WordPress request)
        try:
            # Create a minimal test file
            test_content = b"Test file content"
            files = {'file': ('test.pdf', test_content, 'application/pdf')}
            
            response = requests.post(f"{base_url}/upload", files=files, timeout=10)
            print(f"   Upload Test: Status {response.status_code}")
            print(f"   Upload Response: {response.text[:200]}...")
            
            if response.status_code == 200:
                print("   Upload Test: ✅ Working")
            else:
                print("   Upload Test: ⚠️ Issues detected")
                
        except Exception as e:
            print(f"   Upload Test: ❌ Error: {e}")

def test_wordpress_perspective():
    """Test from WordPress perspective"""
    print("\n🌐 Testing WordPress-style Requests...")
    print("=" * 50)
    
    # This simulates exactly what WordPress does
    base_url = "http://localhost:5000"
    
    try:
        # Simulate WordPress file upload
        import io
        
        # Create a simple PDF-like content
        pdf_content = b"%PDF-1.4\n1 0 obj\n<</Type/Catalog/Pages 2 0 R>>\nendobj\n2 0 obj\n<</Type/Pages/Kids[3 0 R]/Count 1>>\nendobj\n3 0 obj\n<</Type/Page/Parent 2 0 R/MediaBox[0 0 612 792]>>\nendobj\nxref\n0 4\n0000000000 65535 f\n0000000009 00000 n\n0000000074 00000 n\n0000000120 00000 n\ntrailer\n<</Size 4/Root 1 0 R>>\nstartxref\n179\n%%EOF"
        
        # Prepare multipart form data (like WordPress does)
        files = {
            'file': ('test.pdf', io.BytesIO(pdf_content), 'application/pdf')
        }
        
        print("📤 Sending file upload request...")
        response = requests.post(f"{base_url}/upload", files=files, timeout=30)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Body: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✅ WordPress-style upload successful!")
            else:
                print(f"⚠️ Upload failed: {data.get('message')}")
        else:
            print("❌ Upload request failed")
            
    except Exception as e:
        print(f"❌ WordPress-style test failed: {e}")

def check_network_issues():
    """Check for common network issues"""
    print("\n🔧 Checking Network Configuration...")
    print("=" * 50)
    
    import socket
    
    # Check if port 5000 is actually listening
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('localhost', 5000))
        sock.close()
        
        if result == 0:
            print("✅ Port 5000 is open and listening")
        else:
            print("❌ Port 5000 is not accessible")
            print("   Make sure Flask is running with: python flask_api.py")
    except Exception as e:
        print(f"❌ Network test failed: {e}")
    
    # Test different localhost variations
    hosts_to_test = ['localhost', '127.0.0.1', '0.0.0.0']
    for host in hosts_to_test:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex((host, 5000))
            sock.close()
            
            if result == 0:
                print(f"✅ {host}:5000 is accessible")
            else:
                print(f"❌ {host}:5000 is not accessible")
        except Exception as e:
            print(f"❌ {host}:5000 test failed: {e}")

def main():
    """Run all diagnostic tests"""
    print("🚨 WordPress-Flask Connection Diagnostic")
    print("=" * 50)
    
    check_network_issues()
    test_flask_connection()
    test_wordpress_perspective()
    
    print("\n" + "=" * 50)
    print("📋 Next Steps:")
    print("1. If all tests pass, check WordPress error logs")
    print("2. If tests fail, restart Flask with: python flask_api.py")
    print("3. Try different ports if 5000 is problematic")
    print("4. Check firewall/antivirus settings")

if __name__ == "__main__":
    main()
