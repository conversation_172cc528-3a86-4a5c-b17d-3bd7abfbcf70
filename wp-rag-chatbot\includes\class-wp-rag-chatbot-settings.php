<?php
/**
 * Settings functionality for WP RAG Chatbot
 *
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WP RAG Chatbot Settings Class
 *
 * @since 1.0.0
 */
class WP_RAG_Chatbot_Settings {

    /**
     * Settings option name
     *
     * @var string
     * @since 1.0.0
     */
    private static $option_name = 'wp_rag_chatbot_settings';

    /**
     * Default settings
     *
     * @var array
     * @since 1.0.0
     */
    private static $defaults = array(
        'api_endpoint' => 'http://localhost:5000',
        'max_file_size' => 5, // MB
        'allowed_file_types' => array('pdf'),
        'chat_position' => 'bottom-left',
        'chat_enabled' => true,
        'max_message_length' => 500,
        'chat_title' => 'AI Assistant',
        'welcome_message' => 'Hello! How can I help you today?',
        'error_message' => 'Sorry, something went wrong. Please try again.',
        'thinking_message' => 'Thinking...',
        'rate_limit_enabled' => true,
        'rate_limit_requests' => 10,
        'rate_limit_window' => 60 // seconds
    );

    /**
     * Initialize settings
     *
     * @since 1.0.0
     */
    public static function init() {
        add_action('admin_init', array(__CLASS__, 'register_settings'));
    }

    /**
     * Register settings
     *
     * @since 1.0.0
     */
    public static function register_settings() {
        register_setting(
            self::$option_name,
            self::$option_name,
            array(
                'type' => 'array',
                'sanitize_callback' => array(__CLASS__, 'sanitize_settings'),
                'default' => self::$defaults
            )
        );
    }

    /**
     * Get setting value
     *
     * @param string $key Setting key
     * @param mixed $default Default value
     * @return mixed Setting value
     * @since 1.0.0
     */
    public static function get($key, $default = null) {
        $settings = get_option(self::$option_name, self::$defaults);
        
        if (isset($settings[$key])) {
            return $settings[$key];
        }
        
        if ($default !== null) {
            return $default;
        }
        
        return isset(self::$defaults[$key]) ? self::$defaults[$key] : null;
    }

    /**
     * Update setting value
     *
     * @param string $key Setting key
     * @param mixed $value Setting value
     * @return bool True on success
     * @since 1.0.0
     */
    public static function update($key, $value) {
        $settings = get_option(self::$option_name, self::$defaults);
        $settings[$key] = $value;
        return update_option(self::$option_name, $settings);
    }

    /**
     * Get all settings
     *
     * @return array All settings
     * @since 1.0.0
     */
    public static function get_all() {
        return get_option(self::$option_name, self::$defaults);
    }

    /**
     * Update multiple settings
     *
     * @param array $settings Settings array
     * @return bool True on success
     * @since 1.0.0
     */
    public static function update_all($settings) {
        $current_settings = get_option(self::$option_name, self::$defaults);
        $new_settings = array_merge($current_settings, $settings);
        return update_option(self::$option_name, $new_settings);
    }

    /**
     * Reset settings to defaults
     *
     * @return bool True on success
     * @since 1.0.0
     */
    public static function reset() {
        return update_option(self::$option_name, self::$defaults);
    }

    /**
     * Sanitize settings
     *
     * @param array $settings Raw settings
     * @return array Sanitized settings
     * @since 1.0.0
     */
    public static function sanitize_settings($settings) {
        $sanitized = array();
        
        // API endpoint
        if (isset($settings['api_endpoint'])) {
            $sanitized['api_endpoint'] = esc_url_raw($settings['api_endpoint']);
        }
        
        // Max file size
        if (isset($settings['max_file_size'])) {
            $sanitized['max_file_size'] = absint($settings['max_file_size']);
            if ($sanitized['max_file_size'] < 1) {
                $sanitized['max_file_size'] = 1;
            }
            if ($sanitized['max_file_size'] > 50) {
                $sanitized['max_file_size'] = 50;
            }
        }
        
        // Allowed file types
        if (isset($settings['allowed_file_types']) && is_array($settings['allowed_file_types'])) {
            $sanitized['allowed_file_types'] = array_map('sanitize_text_field', $settings['allowed_file_types']);
        }
        
        // Chat position
        if (isset($settings['chat_position'])) {
            $valid_positions = array('bottom-left', 'bottom-right', 'top-left', 'top-right');
            $position = sanitize_text_field($settings['chat_position']);
            $sanitized['chat_position'] = in_array($position, $valid_positions) ? $position : 'bottom-left';
        }
        
        // Chat enabled
        if (isset($settings['chat_enabled'])) {
            $sanitized['chat_enabled'] = (bool) $settings['chat_enabled'];
        }
        
        // Max message length
        if (isset($settings['max_message_length'])) {
            $sanitized['max_message_length'] = absint($settings['max_message_length']);
            if ($sanitized['max_message_length'] < 10) {
                $sanitized['max_message_length'] = 10;
            }
            if ($sanitized['max_message_length'] > 2000) {
                $sanitized['max_message_length'] = 2000;
            }
        }
        
        // Text fields
        $text_fields = array('chat_title', 'welcome_message', 'error_message', 'thinking_message');
        foreach ($text_fields as $field) {
            if (isset($settings[$field])) {
                $sanitized[$field] = sanitize_text_field($settings[$field]);
            }
        }
        
        // Rate limiting
        if (isset($settings['rate_limit_enabled'])) {
            $sanitized['rate_limit_enabled'] = (bool) $settings['rate_limit_enabled'];
        }
        
        if (isset($settings['rate_limit_requests'])) {
            $sanitized['rate_limit_requests'] = absint($settings['rate_limit_requests']);
            if ($sanitized['rate_limit_requests'] < 1) {
                $sanitized['rate_limit_requests'] = 1;
            }
            if ($sanitized['rate_limit_requests'] > 100) {
                $sanitized['rate_limit_requests'] = 100;
            }
        }
        
        if (isset($settings['rate_limit_window'])) {
            $sanitized['rate_limit_window'] = absint($settings['rate_limit_window']);
            if ($sanitized['rate_limit_window'] < 10) {
                $sanitized['rate_limit_window'] = 10;
            }
            if ($sanitized['rate_limit_window'] > 3600) {
                $sanitized['rate_limit_window'] = 3600;
            }
        }
        
        return $sanitized;
    }

    /**
     * Validate API endpoint
     *
     * @param string $endpoint API endpoint URL
     * @return bool True if valid
     * @since 1.0.0
     */
    public static function validate_api_endpoint($endpoint) {
        if (empty($endpoint)) {
            return false;
        }
        
        $parsed = parse_url($endpoint);
        if (!$parsed || !isset($parsed['scheme']) || !isset($parsed['host'])) {
            return false;
        }
        
        $allowed_schemes = array('http', 'https');
        if (!in_array($parsed['scheme'], $allowed_schemes)) {
            return false;
        }
        
        return true;
    }

    /**
     * Test API connection
     *
     * @param string $endpoint API endpoint URL
     * @return array Test result
     * @since 1.0.0
     */
    public static function test_api_connection($endpoint = null) {
        if ($endpoint === null) {
            $endpoint = self::get('api_endpoint');
        }
        
        if (!self::validate_api_endpoint($endpoint)) {
            return array(
                'success' => false,
                'message' => __('Invalid API endpoint URL.', 'wp-rag-chatbot')
            );
        }
        
        $response = wp_remote_get($endpoint . '/health', array(
            'timeout' => 10
        ));
        
        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'message' => sprintf(__('Connection failed: %s', 'wp-rag-chatbot'), $response->get_error_message())
            );
        }
        
        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            return array(
                'success' => false,
                'message' => sprintf(__('API returned status code: %d', 'wp-rag-chatbot'), $response_code)
            );
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        if (!$data || !isset($data['status'])) {
            return array(
                'success' => false,
                'message' => __('Invalid response from API.', 'wp-rag-chatbot')
            );
        }
        
        return array(
            'success' => true,
            'message' => __('API connection successful.', 'wp-rag-chatbot'),
            'data' => $data
        );
    }
}
