<?php
/**
 * Admin functionality for WP RAG Chatbot
 *
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WP RAG Chatbot Admin Class
 *
 * @since 1.0.0
 */
class WP_RAG_Chatbot_Admin {

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'admin_init'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_wp_rag_chatbot_upload_pdf', array($this, 'handle_pdf_upload'));
        add_action('wp_ajax_wp_rag_chatbot_delete_pdf', array($this, 'handle_pdf_delete'));
        add_action('wp_ajax_wp_rag_chatbot_test_api', array($this, 'handle_api_test'));
    }

    /**
     * Add admin menu
     *
     * @since 1.0.0
     */
    public function add_admin_menu() {
        add_menu_page(
            __('AI Chatbot', 'wp-rag-chatbot'),
            __('AI Chatbot', 'wp-rag-chatbot'),
            'manage_options',
            'wp-rag-chatbot',
            array($this, 'admin_page'),
            'dashicons-format-chat',
            30
        );

        add_submenu_page(
            'wp-rag-chatbot',
            __('Settings', 'wp-rag-chatbot'),
            __('Settings', 'wp-rag-chatbot'),
            'manage_options',
            'wp-rag-chatbot-settings',
            array($this, 'settings_page')
        );

        add_submenu_page(
            'wp-rag-chatbot',
            __('PDF Files', 'wp-rag-chatbot'),
            __('PDF Files', 'wp-rag-chatbot'),
            'manage_options',
            'wp-rag-chatbot-files',
            array($this, 'files_page')
        );
    }

    /**
     * Initialize admin settings
     *
     * @since 1.0.0
     */
    public function admin_init() {
        register_setting('wp_rag_chatbot_settings', 'wp_rag_chatbot_settings');

        add_settings_section(
            'wp_rag_chatbot_general',
            __('General Settings', 'wp-rag-chatbot'),
            array($this, 'general_section_callback'),
            'wp_rag_chatbot_settings'
        );

        add_settings_field(
            'openai_api_key',
            __('OpenAI API Key', 'wp-rag-chatbot'),
            array($this, 'openai_api_key_callback'),
            'wp_rag_chatbot_settings',
            'wp_rag_chatbot_general'
        );

        add_settings_field(
            'pinecone_api_key',
            __('Pinecone API Key', 'wp-rag-chatbot'),
            array($this, 'pinecone_api_key_callback'),
            'wp_rag_chatbot_settings',
            'wp_rag_chatbot_general'
        );

        add_settings_field(
            'chat_enabled',
            __('Enable Chat', 'wp-rag-chatbot'),
            array($this, 'chat_enabled_callback'),
            'wp_rag_chatbot_settings',
            'wp_rag_chatbot_general'
        );

        add_settings_field(
            'chat_position',
            __('Chat Position', 'wp-rag-chatbot'),
            array($this, 'chat_position_callback'),
            'wp_rag_chatbot_settings',
            'wp_rag_chatbot_general'
        );
    }

    /**
     * Enqueue admin scripts and styles
     *
     * @param string $hook Current admin page hook
     * @since 1.0.0
     */
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'wp-rag-chatbot') === false) {
            return;
        }

        wp_enqueue_style(
            'wp-rag-chatbot-admin',
            WP_RAG_CHATBOT_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            WP_RAG_CHATBOT_VERSION
        );

        wp_enqueue_script(
            'wp-rag-chatbot-admin',
            WP_RAG_CHATBOT_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            WP_RAG_CHATBOT_VERSION,
            true
        );

        wp_localize_script('wp-rag-chatbot-admin', 'wpRagChatbotAdmin', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('wp_rag_chatbot_admin_nonce'),
            'strings' => array(
                'upload_success' => __('File uploaded successfully!', 'wp-rag-chatbot'),
                'upload_error' => __('Error uploading file.', 'wp-rag-chatbot'),
                'delete_confirm' => __('Are you sure you want to delete this file?', 'wp-rag-chatbot'),
                'delete_success' => __('File deleted successfully!', 'wp-rag-chatbot'),
                'delete_error' => __('Error deleting file.', 'wp-rag-chatbot')
            )
        ));
    }

    /**
     * Main admin page
     *
     * @since 1.0.0
     */
    public function admin_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            
            <div class="wp-rag-chatbot-dashboard">
                <div class="dashboard-widgets">
                    <div class="dashboard-widget">
                        <h3><?php _e('Quick Stats', 'wp-rag-chatbot'); ?></h3>
                        <?php $this->display_stats(); ?>
                    </div>
                    
                    <div class="dashboard-widget">
                        <h3><?php _e('Upload PDF', 'wp-rag-chatbot'); ?></h3>
                        <?php $this->display_upload_form(); ?>
                    </div>
                    
                    <div class="dashboard-widget">
                        <h3><?php _e('Recent Files', 'wp-rag-chatbot'); ?></h3>
                        <?php $this->display_recent_files(); ?>
                    </div>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Settings page
     *
     * @since 1.0.0
     */
    public function settings_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            
            <form method="post" action="options.php">
                <?php
                settings_fields('wp_rag_chatbot_settings');
                do_settings_sections('wp_rag_chatbot_settings');
                submit_button();
                ?>
            </form>
        </div>
        <?php
    }

    /**
     * Files management page
     *
     * @since 1.0.0
     */
    public function files_page() {
        ?>
        <div class="wrap">
            <h1><?php echo esc_html(get_admin_page_title()); ?></h1>
            
            <div class="wp-rag-chatbot-files">
                <?php $this->display_files_table(); ?>
            </div>
        </div>
        <?php
    }

    /**
     * Display dashboard stats
     *
     * @since 1.0.0
     */
    private function display_stats() {
        $uploaded_files = get_option('wp_rag_chatbot_uploaded_files', array());
        $total_files = count($uploaded_files);
        
        echo '<div class="stats-grid">';
        echo '<div class="stat-item">';
        echo '<span class="stat-number">' . esc_html($total_files) . '</span>';
        echo '<span class="stat-label">' . __('Uploaded Files', 'wp-rag-chatbot') . '</span>';
        echo '</div>';
        echo '</div>';
    }

    /**
     * Display upload form
     *
     * @since 1.0.0
     */
    private function display_upload_form() {
        ?>
        <form id="wp-rag-chatbot-upload-form" enctype="multipart/form-data">
            <p>
                <input type="file" id="pdf-file" name="pdf_file" accept=".pdf" required>
            </p>
            <p>
                <button type="submit" class="button button-primary">
                    <?php _e('Upload PDF', 'wp-rag-chatbot'); ?>
                </button>
            </p>
            <div id="upload-progress" style="display: none;">
                <div class="progress-bar">
                    <div class="progress-fill"></div>
                </div>
            </div>
            <div id="upload-message"></div>
        </form>
        <?php
    }

    /**
     * Display recent files
     *
     * @since 1.0.0
     */
    private function display_recent_files() {
        $uploaded_files = get_option('wp_rag_chatbot_uploaded_files', array());
        $recent_files = array_slice(array_reverse($uploaded_files), 0, 5);
        
        if (empty($recent_files)) {
            echo '<p>' . __('No files uploaded yet.', 'wp-rag-chatbot') . '</p>';
            return;
        }
        
        echo '<ul class="recent-files-list">';
        foreach ($recent_files as $file) {
            echo '<li>';
            echo '<strong>' . esc_html($file['name']) . '</strong><br>';
            echo '<small>' . sprintf(__('Uploaded: %s', 'wp-rag-chatbot'), date_i18n(get_option('date_format'), $file['uploaded'])) . '</small>';
            echo '</li>';
        }
        echo '</ul>';
    }

    /**
     * Display files table
     *
     * @since 1.0.0
     */
    private function display_files_table() {
        $uploaded_files = get_option('wp_rag_chatbot_uploaded_files', array());
        
        if (empty($uploaded_files)) {
            echo '<p>' . __('No files uploaded yet.', 'wp-rag-chatbot') . '</p>';
            return;
        }
        
        ?>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th><?php _e('File Name', 'wp-rag-chatbot'); ?></th>
                    <th><?php _e('Size', 'wp-rag-chatbot'); ?></th>
                    <th><?php _e('Upload Date', 'wp-rag-chatbot'); ?></th>
                    <th><?php _e('Status', 'wp-rag-chatbot'); ?></th>
                    <th><?php _e('Actions', 'wp-rag-chatbot'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($uploaded_files as $file_id => $file): ?>
                <tr>
                    <td><?php echo esc_html($file['name']); ?></td>
                    <td><?php echo esc_html(size_format($file['size'])); ?></td>
                    <td><?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $file['uploaded'])); ?></td>
                    <td>
                        <span class="status-badge status-<?php echo esc_attr($file['status']); ?>">
                            <?php echo esc_html(ucfirst($file['status'])); ?>
                        </span>
                    </td>
                    <td>
                        <button class="button button-small delete-file" data-file-id="<?php echo esc_attr($file_id); ?>">
                            <?php _e('Delete', 'wp-rag-chatbot'); ?>
                        </button>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php
    }

    /**
     * General settings section callback
     *
     * @since 1.0.0
     */
    public function general_section_callback() {
        echo '<p>' . __('Configure the general settings for the RAG chatbot.', 'wp-rag-chatbot') . '</p>';
    }

    /**
     * OpenAI API key field callback
     *
     * @since 1.0.0
     */
    public function openai_api_key_callback() {
        $options = get_option('wp_rag_chatbot_settings');
        $value = isset($options['openai_api_key']) ? $options['openai_api_key'] : '';
        echo '<input type="password" name="wp_rag_chatbot_settings[openai_api_key]" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<p class="description">' . __('Your OpenAI API key for GPT and embeddings.', 'wp-rag-chatbot') . '</p>';
    }

    /**
     * Pinecone API key field callback
     *
     * @since 1.0.0
     */
    public function pinecone_api_key_callback() {
        $options = get_option('wp_rag_chatbot_settings');
        $value = isset($options['pinecone_api_key']) ? $options['pinecone_api_key'] : '';
        echo '<input type="password" name="wp_rag_chatbot_settings[pinecone_api_key]" value="' . esc_attr($value) . '" class="regular-text" />';
        echo '<p class="description">' . __('Your Pinecone API key for vector database.', 'wp-rag-chatbot') . '</p>';
    }

    /**
     * Chat enabled field callback
     *
     * @since 1.0.0
     */
    public function chat_enabled_callback() {
        $options = get_option('wp_rag_chatbot_settings');
        $value = isset($options['chat_enabled']) ? $options['chat_enabled'] : true;
        echo '<input type="checkbox" name="wp_rag_chatbot_settings[chat_enabled]" value="1" ' . checked(1, $value, false) . ' />';
        echo '<label>' . __('Enable the chat interface on the frontend', 'wp-rag-chatbot') . '</label>';
    }

    /**
     * Chat position field callback
     *
     * @since 1.0.0
     */
    public function chat_position_callback() {
        $options = get_option('wp_rag_chatbot_settings');
        $value = isset($options['chat_position']) ? $options['chat_position'] : 'bottom-left';

        $positions = array(
            'bottom-left' => __('Bottom Left', 'wp-rag-chatbot'),
            'bottom-right' => __('Bottom Right', 'wp-rag-chatbot'),
            'top-left' => __('Top Left', 'wp-rag-chatbot'),
            'top-right' => __('Top Right', 'wp-rag-chatbot')
        );

        echo '<select name="wp_rag_chatbot_settings[chat_position]">';
        foreach ($positions as $key => $label) {
            echo '<option value="' . esc_attr($key) . '" ' . selected($key, $value, false) . '>' . esc_html($label) . '</option>';
        }
        echo '</select>';
    }

    /**
     * Handle PDF upload via AJAX
     *
     * @since 1.0.0
     */
    public function handle_pdf_upload() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wp_rag_chatbot_admin_nonce')) {
            wp_die(__('Security check failed.', 'wp-rag-chatbot'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have permission to perform this action.', 'wp-rag-chatbot'));
        }

        // Check if file was uploaded
        if (!isset($_FILES['pdf_file']) || $_FILES['pdf_file']['error'] !== UPLOAD_ERR_OK) {
            wp_send_json_error(__('No file uploaded or upload error.', 'wp-rag-chatbot'));
        }

        $file = $_FILES['pdf_file'];

        // Validate file type
        $file_type = wp_check_filetype($file['name']);
        if ($file_type['ext'] !== 'pdf') {
            wp_send_json_error(__('Only PDF files are allowed.', 'wp-rag-chatbot'));
        }

        // Validate file size (5MB max)
        $max_size = 5 * 1024 * 1024; // 5MB
        if ($file['size'] > $max_size) {
            wp_send_json_error(__('File size exceeds 5MB limit.', 'wp-rag-chatbot'));
        }

        // Use file handler to process upload
        require_once WP_RAG_CHATBOT_PLUGIN_PATH . 'includes/class-wp-rag-chatbot-file-handler.php';
        $file_handler = new WP_RAG_Chatbot_File_Handler();

        $result = $file_handler->upload_pdf($file);

        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * Handle PDF deletion via AJAX
     *
     * @since 1.0.0
     */
    public function handle_pdf_delete() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wp_rag_chatbot_admin_nonce')) {
            wp_die(__('Security check failed.', 'wp-rag-chatbot'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have permission to perform this action.', 'wp-rag-chatbot'));
        }

        $file_id = sanitize_text_field($_POST['file_id']);

        if (empty($file_id)) {
            wp_send_json_error(__('Invalid file ID.', 'wp-rag-chatbot'));
        }

        // Use file handler to process deletion
        require_once WP_RAG_CHATBOT_PLUGIN_PATH . 'includes/class-wp-rag-chatbot-file-handler.php';
        $file_handler = new WP_RAG_Chatbot_File_Handler();

        $result = $file_handler->delete_pdf($file_id);

        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }

    /**
     * Handle API connection test via AJAX
     *
     * @since 1.0.0
     */
    public function handle_api_test() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'wp_rag_chatbot_admin_nonce')) {
            wp_die(__('Security check failed.', 'wp-rag-chatbot'));
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have permission to perform this action.', 'wp-rag-chatbot'));
        }

        $endpoint = esc_url_raw($_POST['endpoint']);

        if (empty($endpoint)) {
            wp_send_json_error(__('Invalid endpoint.', 'wp-rag-chatbot'));
        }

        // Use settings class to test connection
        require_once WP_RAG_CHATBOT_PLUGIN_PATH . 'includes/class-wp-rag-chatbot-settings.php';
        $result = WP_RAG_Chatbot_Settings::test_api_connection($endpoint);

        if ($result['success']) {
            wp_send_json_success($result['message']);
        } else {
            wp_send_json_error($result['message']);
        }
    }
}
