<?php
/**
 * File handling functionality for WP RAG Chatbot
 *
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WP RAG Chatbot File Handler Class
 *
 * @since 1.0.0
 */
class WP_RAG_Chatbot_File_Handler {

    /**
     * Upload directory path
     *
     * @var string
     * @since 1.0.0
     */
    private $upload_dir;

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        $upload_dir = wp_upload_dir();
        $this->upload_dir = $upload_dir['basedir'] . '/wp-rag-chatbot';
        
        // Ensure upload directory exists
        if (!file_exists($this->upload_dir)) {
            wp_mkdir_p($this->upload_dir);
        }
    }

    /**
     * Upload PDF file
     *
     * @param array $file File data from $_FILES
     * @return array Result array with success status and message
     * @since 1.0.0
     */
    public function upload_pdf($file) {
        // Validate file
        $validation = $this->validate_pdf_file($file);
        if (!$validation['valid']) {
            return array(
                'success' => false,
                'message' => $validation['message']
            );
        }

        // Generate unique filename
        $filename = $this->generate_unique_filename($file['name']);
        $file_path = $this->upload_dir . '/' . $filename;

        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $file_path)) {
            return array(
                'success' => false,
                'message' => __('Failed to move uploaded file.', 'wp-rag-chatbot')
            );
        }

        // Process file using Python bridge
        require_once WP_RAG_CHATBOT_PLUGIN_PATH . 'includes/class-wp-rag-chatbot-python-bridge.php';
        $python_bridge = new WP_RAG_Chatbot_Python_Bridge();

        $backend_result = $python_bridge->process_pdf($file_path, $filename);

        if (!$backend_result['success']) {
            // Remove file if backend processing failed
            unlink($file_path);
            return $backend_result;
        }

        // Store file information
        $file_info = array(
            'id' => uniqid(),
            'name' => $file['name'],
            'filename' => $filename,
            'size' => $file['size'],
            'path' => $file_path,
            'uploaded' => current_time('timestamp'),
            'status' => 'processed',
            'hash' => $backend_result['hash']
        );

        $this->save_file_info($file_info);

        return array(
            'success' => true,
            'message' => __('PDF uploaded and processed successfully.', 'wp-rag-chatbot'),
            'file_info' => $file_info
        );
    }

    /**
     * Delete PDF file
     *
     * @param string $file_id File ID
     * @return array Result array with success status and message
     * @since 1.0.0
     */
    public function delete_pdf($file_id) {
        $uploaded_files = get_option('wp_rag_chatbot_uploaded_files', array());
        
        if (!isset($uploaded_files[$file_id])) {
            return array(
                'success' => false,
                'message' => __('File not found.', 'wp-rag-chatbot')
            );
        }

        $file_info = $uploaded_files[$file_id];
        
        // Delete physical file
        if (file_exists($file_info['path'])) {
            unlink($file_info['path']);
        }

        // Remove from database
        unset($uploaded_files[$file_id]);
        update_option('wp_rag_chatbot_uploaded_files', $uploaded_files);

        return array(
            'success' => true,
            'message' => __('File deleted successfully.', 'wp-rag-chatbot')
        );
    }

    /**
     * Validate PDF file
     *
     * @param array $file File data
     * @return array Validation result
     * @since 1.0.0
     */
    private function validate_pdf_file($file) {
        // Check for upload errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            return array(
                'valid' => false,
                'message' => $this->get_upload_error_message($file['error'])
            );
        }

        // Check file type
        $file_type = wp_check_filetype($file['name']);
        if ($file_type['ext'] !== 'pdf') {
            return array(
                'valid' => false,
                'message' => __('Only PDF files are allowed.', 'wp-rag-chatbot')
            );
        }

        // Check MIME type
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        $mime_type = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        if ($mime_type !== 'application/pdf') {
            return array(
                'valid' => false,
                'message' => __('Invalid PDF file.', 'wp-rag-chatbot')
            );
        }

        // Check file size (5MB max)
        $max_size = 5 * 1024 * 1024; // 5MB
        if ($file['size'] > $max_size) {
            return array(
                'valid' => false,
                'message' => sprintf(__('File size exceeds %s limit.', 'wp-rag-chatbot'), size_format($max_size))
            );
        }

        return array('valid' => true);
    }

    /**
     * Generate unique filename
     *
     * @param string $original_name Original filename
     * @return string Unique filename
     * @since 1.0.0
     */
    private function generate_unique_filename($original_name) {
        $info = pathinfo($original_name);
        $name = sanitize_file_name($info['filename']);
        $ext = $info['extension'];
        
        $timestamp = current_time('timestamp');
        $random = wp_generate_password(8, false);
        
        return $name . '_' . $timestamp . '_' . $random . '.' . $ext;
    }



    /**
     * Save file information to database
     *
     * @param array $file_info File information
     * @since 1.0.0
     */
    private function save_file_info($file_info) {
        $uploaded_files = get_option('wp_rag_chatbot_uploaded_files', array());
        $uploaded_files[$file_info['id']] = $file_info;
        update_option('wp_rag_chatbot_uploaded_files', $uploaded_files);
    }

    /**
     * Get upload error message
     *
     * @param int $error_code Error code
     * @return string Error message
     * @since 1.0.0
     */
    private function get_upload_error_message($error_code) {
        switch ($error_code) {
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                return __('File is too large.', 'wp-rag-chatbot');
            case UPLOAD_ERR_PARTIAL:
                return __('File was only partially uploaded.', 'wp-rag-chatbot');
            case UPLOAD_ERR_NO_FILE:
                return __('No file was uploaded.', 'wp-rag-chatbot');
            case UPLOAD_ERR_NO_TMP_DIR:
                return __('Missing temporary folder.', 'wp-rag-chatbot');
            case UPLOAD_ERR_CANT_WRITE:
                return __('Failed to write file to disk.', 'wp-rag-chatbot');
            case UPLOAD_ERR_EXTENSION:
                return __('File upload stopped by extension.', 'wp-rag-chatbot');
            default:
                return __('Unknown upload error.', 'wp-rag-chatbot');
        }
    }
}
