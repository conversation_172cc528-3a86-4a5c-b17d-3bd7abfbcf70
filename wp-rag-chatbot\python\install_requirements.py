#!/usr/bin/env python3
"""
Install required packages for WP RAG Chatbot
This script installs packages compatible with Python 3.6+
"""

import subprocess
import sys
import json

def install_package(package):
    """Install a single package"""
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        return True, f"Successfully installed {package}"
    except subprocess.CalledProcessError as e:
        return False, f"Failed to install {package}: {str(e)}"

def main():
    """Install all required packages"""
    # Packages compatible with Python 3.6
    packages = [
        'PyMuPDF==1.23.0',
        'requests==2.31.0', 
        'python-dotenv==1.0.0',
        'openai==0.28.1',
        'pinecone-client==2.2.4'
    ]
    
    # Try newer packages first, fall back to compatible versions
    langchain_packages = [
        'langchain==0.0.350',
        'langchain-openai==0.0.2',
        'langchain-pinecone==0.0.1'
    ]
    
    results = {}
    
    print("Installing Python packages for WP RAG Chatbot...")
    print("=" * 50)
    
    # Install basic packages
    for package in packages:
        print(f"Installing {package}...")
        success, message = install_package(package)
        results[package] = {'success': success, 'message': message}
        print(f"  {'✅' if success else '❌'} {message}")
    
    # Install LangChain packages (may fail on older Python)
    for package in langchain_packages:
        print(f"Installing {package}...")
        success, message = install_package(package)
        results[package] = {'success': success, 'message': message}
        print(f"  {'✅' if success else '❌'} {message}")
        
        if not success:
            # Try without version constraint
            base_package = package.split('==')[0]
            print(f"Retrying {base_package} without version constraint...")
            success, message = install_package(base_package)
            results[base_package] = {'success': success, 'message': message}
            print(f"  {'✅' if success else '❌'} {message}")
    
    print("\n" + "=" * 50)
    print("Installation Summary:")
    
    successful = sum(1 for r in results.values() if r['success'])
    total = len(results)
    
    print(f"Successfully installed: {successful}/{total} packages")
    
    if successful == total:
        print("✅ All packages installed successfully!")
    else:
        print("⚠️ Some packages failed to install")
        print("Failed packages:")
        for package, result in results.items():
            if not result['success']:
                print(f"  ❌ {package}: {result['message']}")
    
    # Return results as JSON for WordPress
    return {
        'success': successful > 0,
        'total_packages': total,
        'successful_packages': successful,
        'results': results
    }

if __name__ == "__main__":
    try:
        result = main()
        print(json.dumps(result))
    except Exception as e:
        error_result = {
            'success': False,
            'error': str(e)
        }
        print(json.dumps(error_result))
