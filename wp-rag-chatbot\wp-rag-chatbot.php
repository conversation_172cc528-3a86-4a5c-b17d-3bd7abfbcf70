<?php
/**
 * Plugin Name: WP RAG Chatbot
 * Plugin URI: https://github.com/your-username/wp-rag-chatbot
 * Description: A WordPress plugin that enables users to interact with a company-specific AI assistant using RAG (Retrieval-Augmented Generation) functionality with PDF document support.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://yourwebsite.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: wp-rag-chatbot
 * Domain Path: /languages
 * Requires at least: 6.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('WP_RAG_CHATBOT_VERSION', '1.0.1');
define('WP_RAG_CHATBOT_PLUGIN_URL', plugin_dir_url(__FILE__));
define('WP_RAG_CHATBOT_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('WP_RAG_CHATBOT_PLUGIN_BASENAME', plugin_basename(__FILE__));
define('WP_RAG_CHATBOT_TEXT_DOMAIN', 'wp-rag-chatbot');

/**
 * Main WP RAG Chatbot Class
 *
 * @since 1.0.0
 */
class WP_RAG_Chatbot {

    /**
     * Single instance of the class
     *
     * @var WP_RAG_Chatbot
     * @since 1.0.0
     */
    private static $instance = null;

    /**
     * Get single instance of the class
     *
     * @return WP_RAG_Chatbot
     * @since 1.0.0
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    private function __construct() {
        $this->init_hooks();
        $this->load_dependencies();
    }

    /**
     * Initialize WordPress hooks
     *
     * @since 1.0.0
     */
    private function init_hooks() {
        // Activation and deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        register_uninstall_hook(__FILE__, array('WP_RAG_Chatbot', 'uninstall'));

        // Initialize plugin
        add_action('init', array($this, 'init'));
        add_action('plugins_loaded', array($this, 'load_textdomain'));
    }

    /**
     * Load plugin dependencies
     *
     * @since 1.0.0
     */
    private function load_dependencies() {
        // Load admin functionality
        if (is_admin()) {
            require_once WP_RAG_CHATBOT_PLUGIN_PATH . 'admin/class-wp-rag-chatbot-admin.php';
            new WP_RAG_Chatbot_Admin();
        }

        // Load public functionality
        require_once WP_RAG_CHATBOT_PLUGIN_PATH . 'public/class-wp-rag-chatbot-public.php';
        new WP_RAG_Chatbot_Public();

        // Load includes
        require_once WP_RAG_CHATBOT_PLUGIN_PATH . 'includes/class-wp-rag-chatbot-api.php';
        require_once WP_RAG_CHATBOT_PLUGIN_PATH . 'includes/class-wp-rag-chatbot-file-handler.php';
        require_once WP_RAG_CHATBOT_PLUGIN_PATH . 'includes/class-wp-rag-chatbot-settings.php';
        require_once WP_RAG_CHATBOT_PLUGIN_PATH . 'install.php';

        // Initialize API
        new WP_RAG_Chatbot_API();
    }

    /**
     * Initialize plugin
     *
     * @since 1.0.0
     */
    public function init() {
        // Initialize settings
        WP_RAG_Chatbot_Settings::init();
    }

    /**
     * Load plugin textdomain for internationalization
     *
     * @since 1.0.0
     */
    public function load_textdomain() {
        load_plugin_textdomain(
            WP_RAG_CHATBOT_TEXT_DOMAIN,
            false,
            dirname(WP_RAG_CHATBOT_PLUGIN_BASENAME) . '/languages'
        );
    }

    /**
     * Plugin activation
     *
     * @since 1.0.0
     */
    public function activate() {
        // Check system requirements
        $requirements = WP_RAG_Chatbot_Install::check_requirements();
        if (!empty($requirements)) {
            wp_die(
                '<h1>' . __('Plugin Activation Failed', 'wp-rag-chatbot') . '</h1>' .
                '<p>' . __('The following requirements are not met:', 'wp-rag-chatbot') . '</p>' .
                '<ul><li>' . implode('</li><li>', $requirements) . '</li></ul>',
                __('Plugin Activation Failed', 'wp-rag-chatbot'),
                array('back_link' => true)
            );
        }

        // Run installation
        WP_RAG_Chatbot_Install::install();
    }

    /**
     * Plugin deactivation
     *
     * @since 1.0.0
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Plugin uninstall
     *
     * @since 1.0.0
     */
    public static function uninstall() {
        // Use installation class for cleanup
        WP_RAG_Chatbot_Install::uninstall();
    }



    /**
     * Get plugin option
     *
     * @param string $key Option key
     * @param mixed $default Default value
     * @return mixed Option value
     * @since 1.0.0
     */
    public static function get_option($key, $default = null) {
        $options = get_option('wp_rag_chatbot_settings', array());
        return isset($options[$key]) ? $options[$key] : $default;
    }

    /**
     * Update plugin option
     *
     * @param string $key Option key
     * @param mixed $value Option value
     * @since 1.0.0
     */
    public static function update_option($key, $value) {
        $options = get_option('wp_rag_chatbot_settings', array());
        $options[$key] = $value;
        update_option('wp_rag_chatbot_settings', $options);
    }
}

// Initialize the plugin
WP_RAG_Chatbot::get_instance();
