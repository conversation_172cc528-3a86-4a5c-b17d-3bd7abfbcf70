#!/usr/bin/env python3
"""
Debug Python path and package installation
"""

import sys
import os
import json

def main():
    """Debug Python environment"""
    try:
        info = {
            'python_version': sys.version,
            'python_executable': sys.executable,
            'python_path': sys.path,
            'current_directory': os.getcwd(),
            'environment_variables': {
                'PATH': os.environ.get('PATH', ''),
                'PYTHONPATH': os.environ.get('PYTHONPATH', ''),
                'PYTHON': os.environ.get('PYTHON', '')
            }
        }
        
        # Test package imports
        packages = ['PyMuPDF', 'requests', 'openai', 'python_dotenv', 'pinecone', 'langchain']
        package_status = {}
        
        for package in packages:
            try:
                if package == 'PyMuPDF':
                    import fitz
                    package_status[package] = {'installed': True, 'version': fitz.__version__ if hasattr(fitz, '__version__') else 'unknown'}
                elif package == 'python_dotenv':
                    import dotenv
                    package_status[package] = {'installed': True, 'version': dotenv.__version__ if hasattr(dotenv, '__version__') else 'unknown'}
                else:
                    module = __import__(package)
                    package_status[package] = {'installed': True, 'version': getattr(module, '__version__', 'unknown')}
            except ImportError as e:
                package_status[package] = {'installed': False, 'error': str(e)}
        
        info['packages'] = package_status
        
        return {
            'success': True,
            'info': info
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

if __name__ == "__main__":
    try:
        result = main()
        print(json.dumps(result, indent=2))
    except Exception as e:
        error_result = {
            'success': False,
            'error': str(e)
        }
        print(json.dumps(error_result))
