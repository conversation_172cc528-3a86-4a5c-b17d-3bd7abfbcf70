/**
 * Admin JavaScript for WP RAG Chatbot
 * 
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Admin Class
    class WPRagChatbotAdmin {
        constructor() {
            this.init();
        }

        init() {
            this.bindEvents();
            this.initFileUpload();
        }

        bindEvents() {
            // File upload form
            $('#wp-rag-chatbot-upload-form').on('submit', (e) => {
                e.preventDefault();
                this.handleFileUpload();
            });

            // Delete file buttons
            $(document).on('click', '.delete-file', (e) => {
                e.preventDefault();
                this.handleFileDelete($(e.target));
            });

            // Python environment test button
            $(document).on('click', '#test-python-environment', (e) => {
                e.preventDefault();
                this.testAPIConnection();
            });

            // Settings form validation
            $('form[action="options.php"]').on('submit', (e) => {
                if (!this.validateSettings()) {
                    e.preventDefault();
                }
            });
        }

        initFileUpload() {
            const $fileInput = $('#pdf-file');
            const $uploadForm = $('#wp-rag-chatbot-upload-form');

            // Drag and drop functionality
            $uploadForm.on('dragover dragenter', (e) => {
                e.preventDefault();
                e.stopPropagation();
                $uploadForm.addClass('drag-over');
            });

            $uploadForm.on('dragleave dragend', (e) => {
                e.preventDefault();
                e.stopPropagation();
                $uploadForm.removeClass('drag-over');
            });

            $uploadForm.on('drop', (e) => {
                e.preventDefault();
                e.stopPropagation();
                $uploadForm.removeClass('drag-over');

                const files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    $fileInput[0].files = files;
                    this.handleFileUpload();
                }
            });

            // File input change
            $fileInput.on('change', () => {
                if ($fileInput[0].files.length > 0) {
                    this.validateFile($fileInput[0].files[0]);
                }
            });
        }

        validateFile(file) {
            const $message = $('#upload-message');
            
            // Check file type
            if (file.type !== 'application/pdf') {
                this.showMessage('Only PDF files are allowed.', 'error');
                return false;
            }

            // Check file size (5MB)
            const maxSize = 5 * 1024 * 1024;
            if (file.size > maxSize) {
                this.showMessage('File size exceeds 5MB limit.', 'error');
                return false;
            }

            this.hideMessage();
            return true;
        }

        async handleFileUpload() {
            const $form = $('#wp-rag-chatbot-upload-form');
            const $fileInput = $('#pdf-file');
            const $progress = $('#upload-progress');
            const $progressFill = $('.progress-fill');
            const $submitBtn = $form.find('button[type="submit"]');

            const file = $fileInput[0].files[0];
            if (!file) {
                this.showMessage('Please select a file.', 'error');
                return;
            }

            if (!this.validateFile(file)) {
                return;
            }

            // Show progress
            $progress.show();
            $progressFill.css('width', '0%');
            $submitBtn.prop('disabled', true).text('Uploading...');

            try {
                // Simulate progress
                this.animateProgress($progressFill, 30);

                const formData = new FormData();
                formData.append('action', 'wp_rag_chatbot_upload_pdf');
                formData.append('nonce', wpRagChatbotAdmin.nonce);
                formData.append('pdf_file', file);

                const response = await $.ajax({
                    url: wpRagChatbotAdmin.ajax_url,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    xhr: () => {
                        const xhr = new window.XMLHttpRequest();
                        xhr.upload.addEventListener('progress', (e) => {
                            if (e.lengthComputable) {
                                const percentComplete = (e.loaded / e.total) * 100;
                                $progressFill.css('width', percentComplete + '%');
                            }
                        });
                        return xhr;
                    }
                });

                if (response.success) {
                    $progressFill.css('width', '100%');
                    this.showMessage(wpRagChatbotAdmin.strings.upload_success, 'success');
                    $fileInput.val('');
                    
                    // Refresh page after delay
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    this.showMessage(response.data || wpRagChatbotAdmin.strings.upload_error, 'error');
                }

            } catch (error) {
                console.error('Upload error:', error);
                this.showMessage(wpRagChatbotAdmin.strings.upload_error, 'error');
            } finally {
                $progress.hide();
                $submitBtn.prop('disabled', false).text('Upload PDF');
            }
        }

        async handleFileDelete($button) {
            if (!confirm(wpRagChatbotAdmin.strings.delete_confirm)) {
                return;
            }

            const fileId = $button.data('file-id');
            const $row = $button.closest('tr');

            $button.prop('disabled', true).text('Deleting...');

            try {
                const response = await $.ajax({
                    url: wpRagChatbotAdmin.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'wp_rag_chatbot_delete_pdf',
                        nonce: wpRagChatbotAdmin.nonce,
                        file_id: fileId
                    }
                });

                if (response.success) {
                    $row.fadeOut(() => {
                        $row.remove();
                        this.showGlobalMessage(wpRagChatbotAdmin.strings.delete_success, 'success');
                    });
                } else {
                    this.showGlobalMessage(response.data || wpRagChatbotAdmin.strings.delete_error, 'error');
                    $button.prop('disabled', false).text('Delete');
                }

            } catch (error) {
                console.error('Delete error:', error);
                this.showGlobalMessage(wpRagChatbotAdmin.strings.delete_error, 'error');
                $button.prop('disabled', false).text('Delete');
            }
        }

        async testAPIConnection() {
            const $button = $('#test-python-environment');
            const $result = $('#python-test-result');
            const openaiKey = $('input[name="wp_rag_chatbot_settings[openai_api_key]"]').val();
            const pineconeKey = $('input[name="wp_rag_chatbot_settings[pinecone_api_key]"]').val();

            if (!openaiKey || !pineconeKey) {
                this.showAPITestResult('Please enter both API keys first.', 'error');
                return;
            }

            $button.prop('disabled', true).html('<span class="spinner"></span>Testing...');

            try {
                const response = await $.ajax({
                    url: wpRagChatbotAdmin.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'wp_rag_chatbot_test_python',
                        nonce: wpRagChatbotAdmin.nonce,
                        openai_key: openaiKey,
                        pinecone_key: pineconeKey
                    }
                });

                if (response.success) {
                    this.showAPITestResult('Python environment and API keys working!', 'success');
                } else {
                    this.showAPITestResult(response.data || 'Python environment test failed.', 'error');
                }

            } catch (error) {
                console.error('Python test error:', error);
                this.showAPITestResult('Python environment test failed.', 'error');
            } finally {
                $button.prop('disabled', false).text('Test Environment');
            }
        }

        validateSettings() {
            const openaiKey = $('input[name="wp_rag_chatbot_settings[openai_api_key]"]').val();
            const pineconeKey = $('input[name="wp_rag_chatbot_settings[pinecone_api_key]"]').val();

            if (!openaiKey || openaiKey.trim() === '') {
                alert('OpenAI API Key is required.');
                return false;
            }

            if (!pineconeKey || pineconeKey.trim() === '') {
                alert('Pinecone API Key is required.');
                return false;
            }

            // Basic API key validation
            if (!openaiKey.startsWith('sk-')) {
                alert('OpenAI API Key should start with "sk-".');
                return false;
            }

            return true;
        }

        animateProgress($element, targetPercent) {
            let currentPercent = 0;
            const interval = setInterval(() => {
                currentPercent += 2;
                $element.css('width', currentPercent + '%');
                
                if (currentPercent >= targetPercent) {
                    clearInterval(interval);
                }
            }, 50);
        }

        showMessage(message, type) {
            const $message = $('#upload-message');
            $message.removeClass('success error')
                   .addClass(type)
                   .text(message)
                   .show();

            if (type === 'success') {
                setTimeout(() => {
                    $message.fadeOut();
                }, 5000);
            }
        }

        hideMessage() {
            $('#upload-message').hide();
        }

        showGlobalMessage(message, type) {
            // Create or update global message
            let $notice = $('.wp-rag-chatbot-notice');
            
            if ($notice.length === 0) {
                $notice = $('<div class="notice wp-rag-chatbot-notice is-dismissible"><p></p></div>');
                $('.wrap h1').after($notice);
            }

            $notice.removeClass('notice-success notice-error')
                   .addClass('notice-' + type)
                   .find('p')
                   .text(message);

            $notice.show();

            // Auto-hide success messages
            if (type === 'success') {
                setTimeout(() => {
                    $notice.fadeOut();
                }, 5000);
            }
        }

        showAPITestResult(message, type) {
            const $result = $('#python-test-result');

            if ($result.length === 0) {
                $('input[name="wp_rag_chatbot_settings[pinecone_api_key]"]').after('<div id="python-test-result" class="api-test-result"></div>');
            }

            $('#python-test-result').removeClass('success error')
                                    .addClass(type)
                                    .text(message)
                                    .show();

            setTimeout(() => {
                $('#python-test-result').fadeOut();
            }, 5000);
        }
    }

    // Initialize when DOM is ready
    $(document).ready(function() {
        if (typeof wpRagChatbotAdmin !== 'undefined') {
            new WPRagChatbotAdmin();
        }
    });

})(jQuery);
