<?php
/**
 * Installation and setup script for WP RAG Chatbot
 * 
 * This script helps with the initial setup and configuration
 * Run this script after plugin activation to ensure proper setup
 *
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WP RAG Chatbot Installation Class
 */
class WP_RAG_Chatbot_Install {

    /**
     * Run installation
     */
    public static function install() {
        self::create_directories();
        self::create_database_tables();
        self::set_default_options();
        self::create_htaccess_protection();
        self::schedule_cleanup_task();
        
        // Flush rewrite rules
        flush_rewrite_rules();
        
        // Set installation flag
        update_option('wp_rag_chatbot_installed', true);
        update_option('wp_rag_chatbot_version', WP_RAG_CHATBOT_VERSION);
    }

    /**
     * Create necessary directories
     */
    private static function create_directories() {
        $upload_dir = wp_upload_dir();
        $chatbot_dir = $upload_dir['basedir'] . '/wp-rag-chatbot';
        
        // Create main upload directory
        if (!file_exists($chatbot_dir)) {
            wp_mkdir_p($chatbot_dir);
        }
        
        // Create subdirectories
        $subdirs = array('pdfs', 'logs', 'cache');
        foreach ($subdirs as $subdir) {
            $dir_path = $chatbot_dir . '/' . $subdir;
            if (!file_exists($dir_path)) {
                wp_mkdir_p($dir_path);
            }
        }
    }

    /**
     * Create database tables if needed
     */
    private static function create_database_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Chat logs table
        $table_name = $wpdb->prefix . 'rag_chatbot_logs';
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            user_id bigint(20) DEFAULT NULL,
            session_id varchar(255) NOT NULL,
            message text NOT NULL,
            response text NOT NULL,
            timestamp datetime DEFAULT CURRENT_TIMESTAMP,
            ip_address varchar(45) DEFAULT NULL,
            user_agent text DEFAULT NULL,
            PRIMARY KEY (id),
            KEY user_id (user_id),
            KEY session_id (session_id),
            KEY timestamp (timestamp)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
        
        // File tracking table
        $table_name = $wpdb->prefix . 'rag_chatbot_files';
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            file_id varchar(255) NOT NULL,
            original_name varchar(255) NOT NULL,
            file_path varchar(500) NOT NULL,
            file_size bigint(20) NOT NULL,
            file_hash varchar(64) NOT NULL,
            upload_date datetime DEFAULT CURRENT_TIMESTAMP,
            status varchar(50) DEFAULT 'pending',
            chunks_count int DEFAULT 0,
            user_id bigint(20) DEFAULT NULL,
            PRIMARY KEY (id),
            UNIQUE KEY file_id (file_id),
            KEY file_hash (file_hash),
            KEY status (status),
            KEY user_id (user_id)
        ) $charset_collate;";
        
        dbDelta($sql);
    }

    /**
     * Set default plugin options
     */
    private static function set_default_options() {
        $default_options = array(
            'api_endpoint' => 'http://localhost:5000',
            'max_file_size' => 5, // MB
            'allowed_file_types' => array('pdf'),
            'chat_position' => 'bottom-left',
            'chat_enabled' => true,
            'max_message_length' => 500,
            'chat_title' => __('AI Assistant', 'wp-rag-chatbot'),
            'welcome_message' => __('Hello! How can I help you today?', 'wp-rag-chatbot'),
            'error_message' => __('Sorry, something went wrong. Please try again.', 'wp-rag-chatbot'),
            'thinking_message' => __('Thinking...', 'wp-rag-chatbot'),
            'rate_limit_enabled' => true,
            'rate_limit_requests' => 10,
            'rate_limit_window' => 60, // seconds
            'log_conversations' => false,
            'delete_logs_after' => 30, // days
            'enable_analytics' => false,
            'chat_theme' => 'default'
        );

        // Only set if not already exists
        if (!get_option('wp_rag_chatbot_settings')) {
            add_option('wp_rag_chatbot_settings', $default_options);
        }
        
        // Initialize empty file list
        if (!get_option('wp_rag_chatbot_uploaded_files')) {
            add_option('wp_rag_chatbot_uploaded_files', array());
        }
    }

    /**
     * Create .htaccess protection for upload directory
     */
    private static function create_htaccess_protection() {
        $upload_dir = wp_upload_dir();
        $chatbot_dir = $upload_dir['basedir'] . '/wp-rag-chatbot';
        $htaccess_file = $chatbot_dir . '/.htaccess';
        
        if (!file_exists($htaccess_file)) {
            $htaccess_content = "# WP RAG Chatbot Security\n";
            $htaccess_content .= "Options -Indexes\n";
            $htaccess_content .= "Options -ExecCGI\n";
            $htaccess_content .= "\n";
            $htaccess_content .= "# Deny access to all files by default\n";
            $htaccess_content .= "<Files *>\n";
            $htaccess_content .= "    Order allow,deny\n";
            $htaccess_content .= "    Deny from all\n";
            $htaccess_content .= "</Files>\n";
            $htaccess_content .= "\n";
            $htaccess_content .= "# Allow access to specific file types for authorized users only\n";
            $htaccess_content .= "<FilesMatch \"\\.(pdf)$\">\n";
            $htaccess_content .= "    Order allow,deny\n";
            $htaccess_content .= "    Deny from all\n";
            $htaccess_content .= "</FilesMatch>\n";
            
            file_put_contents($htaccess_file, $htaccess_content);
        }
        
        // Create index.php to prevent directory browsing
        $index_file = $chatbot_dir . '/index.php';
        if (!file_exists($index_file)) {
            file_put_contents($index_file, "<?php\n// Silence is golden.\n");
        }
    }

    /**
     * Schedule cleanup tasks
     */
    private static function schedule_cleanup_task() {
        if (!wp_next_scheduled('wp_rag_chatbot_cleanup')) {
            wp_schedule_event(time(), 'daily', 'wp_rag_chatbot_cleanup');
        }
    }

    /**
     * Run uninstallation
     */
    public static function uninstall() {
        self::remove_directories();
        self::remove_database_tables();
        self::remove_options();
        self::clear_scheduled_tasks();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Remove plugin directories and files
     */
    private static function remove_directories() {
        $upload_dir = wp_upload_dir();
        $chatbot_dir = $upload_dir['basedir'] . '/wp-rag-chatbot';
        
        if (file_exists($chatbot_dir)) {
            self::delete_directory_recursive($chatbot_dir);
        }
    }

    /**
     * Remove database tables
     */
    private static function remove_database_tables() {
        global $wpdb;
        
        $tables = array(
            $wpdb->prefix . 'rag_chatbot_logs',
            $wpdb->prefix . 'rag_chatbot_files'
        );
        
        foreach ($tables as $table) {
            $wpdb->query("DROP TABLE IF EXISTS $table");
        }
    }

    /**
     * Remove plugin options
     */
    private static function remove_options() {
        $options = array(
            'wp_rag_chatbot_settings',
            'wp_rag_chatbot_uploaded_files',
            'wp_rag_chatbot_installed',
            'wp_rag_chatbot_version'
        );
        
        foreach ($options as $option) {
            delete_option($option);
        }
    }

    /**
     * Clear scheduled tasks
     */
    private static function clear_scheduled_tasks() {
        wp_clear_scheduled_hook('wp_rag_chatbot_cleanup');
    }

    /**
     * Recursively delete directory
     */
    private static function delete_directory_recursive($dir) {
        if (!file_exists($dir)) {
            return;
        }

        $files = array_diff(scandir($dir), array('.', '..'));
        
        foreach ($files as $file) {
            $file_path = $dir . '/' . $file;
            if (is_dir($file_path)) {
                self::delete_directory_recursive($file_path);
            } else {
                unlink($file_path);
            }
        }
        
        rmdir($dir);
    }

    /**
     * Check system requirements
     */
    public static function check_requirements() {
        $requirements = array();
        
        // PHP version
        if (version_compare(PHP_VERSION, '7.4', '<')) {
            $requirements[] = sprintf(
                __('PHP version 7.4 or higher is required. You are running version %s.', 'wp-rag-chatbot'),
                PHP_VERSION
            );
        }
        
        // WordPress version
        global $wp_version;
        if (version_compare($wp_version, '6.0', '<')) {
            $requirements[] = sprintf(
                __('WordPress version 6.0 or higher is required. You are running version %s.', 'wp-rag-chatbot'),
                $wp_version
            );
        }
        
        // Required PHP extensions
        $required_extensions = array('curl', 'json', 'fileinfo');
        foreach ($required_extensions as $extension) {
            if (!extension_loaded($extension)) {
                $requirements[] = sprintf(
                    __('PHP extension "%s" is required but not installed.', 'wp-rag-chatbot'),
                    $extension
                );
            }
        }
        
        // File permissions
        $upload_dir = wp_upload_dir();
        if (!is_writable($upload_dir['basedir'])) {
            $requirements[] = __('Upload directory is not writable.', 'wp-rag-chatbot');
        }
        
        return $requirements;
    }
}

// Hook into WordPress
add_action('wp_rag_chatbot_cleanup', array('WP_RAG_Chatbot_Install', 'cleanup_old_files'));

/**
 * Cleanup old files and logs
 */
function wp_rag_chatbot_cleanup_old_files() {
    global $wpdb;
    
    $settings = get_option('wp_rag_chatbot_settings', array());
    $delete_after_days = isset($settings['delete_logs_after']) ? $settings['delete_logs_after'] : 30;
    
    // Delete old chat logs
    $table_name = $wpdb->prefix . 'rag_chatbot_logs';
    $wpdb->query($wpdb->prepare(
        "DELETE FROM $table_name WHERE timestamp < DATE_SUB(NOW(), INTERVAL %d DAY)",
        $delete_after_days
    ));
    
    // Clean up orphaned files
    $upload_dir = wp_upload_dir();
    $chatbot_dir = $upload_dir['basedir'] . '/wp-rag-chatbot';
    
    // Add cleanup logic here
}
