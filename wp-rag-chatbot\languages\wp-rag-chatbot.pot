# Copyright (C) 2024 WP RAG Chatbot
# This file is distributed under the same license as the WP RAG Chatbot package.
msgid ""
msgstr ""
"Project-Id-Version: WP RAG Chatbot 1.0.0\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/wp-rag-chatbot\n"
"POT-Creation-Date: 2024-01-01 00:00:00+00:00\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"PO-Revision-Date: 2024-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: WordPress\n"

#: admin/class-wp-rag-chatbot-admin.php:40
msgid "AI Chatbot"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:52
msgid "Settings"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:60
msgid "PDF Files"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:78
msgid "General Settings"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:87
msgid "API Endpoint"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:95
msgid "Enable Chat"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:103
msgid "Chat Position"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:125
msgid "File uploaded successfully!"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:126
msgid "Error uploading file."
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:127
msgid "Are you sure you want to delete this file?"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:128
msgid "File deleted successfully!"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:129
msgid "Error deleting file."
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:142
msgid "Quick Stats"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:147
msgid "Upload PDF"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:152
msgid "Recent Files"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:185
msgid "Uploaded Files"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:195
msgid "Upload PDF"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:217
msgid "No files uploaded yet."
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:224
#, php-format
msgid "Uploaded: %s"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:238
msgid "No files uploaded yet."
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:245
msgid "File Name"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:246
msgid "Size"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:247
msgid "Upload Date"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:248
msgid "Status"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:249
msgid "Actions"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:262
msgid "Delete"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:275
msgid "Configure the general settings for the RAG chatbot."
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:284
msgid "The URL of your Python Flask API backend."
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:294
msgid "Enable the chat interface on the frontend"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:306
msgid "Bottom Left"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:307
msgid "Bottom Right"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:308
msgid "Top Left"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:309
msgid "Top Right"
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:322
#: admin/class-wp-rag-chatbot-admin.php:330
#: admin/class-wp-rag-chatbot-admin.php:450
#: admin/class-wp-rag-chatbot-admin.php:458
#: admin/class-wp-rag-chatbot-admin.php:483
#: admin/class-wp-rag-chatbot-admin.php:491
msgid "Security check failed."
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:326
#: admin/class-wp-rag-chatbot-admin.php:334
#: admin/class-wp-rag-chatbot-admin.php:454
#: admin/class-wp-rag-chatbot-admin.php:462
#: admin/class-wp-rag-chatbot-admin.php:487
#: admin/class-wp-rag-chatbot-admin.php:495
msgid "You do not have permission to perform this action."
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:338
msgid "No file uploaded or upload error."
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:346
msgid "Only PDF files are allowed."
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:351
msgid "File size exceeds 5MB limit."
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:466
msgid "Invalid file ID."
msgstr ""

#: admin/class-wp-rag-chatbot-admin.php:499
msgid "Invalid endpoint."
msgstr ""

#: public/class-wp-rag-chatbot-public.php:52
msgid "Hello! How can I help you today?"
msgstr ""

#: public/class-wp-rag-chatbot-public.php:53
msgid "Type your message..."
msgstr ""

#: public/class-wp-rag-chatbot-public.php:54
msgid "Send"
msgstr ""

#: public/class-wp-rag-chatbot-public.php:55
msgid "Sorry, something went wrong. Please try again."
msgstr ""

#: public/class-wp-rag-chatbot-public.php:56
msgid "Thinking..."
msgstr ""

#: public/class-wp-rag-chatbot-public.php:57
msgid "Close"
msgstr ""

#: public/class-wp-rag-chatbot-public.php:58
msgid "Minimize"
msgstr ""

#: public/class-wp-rag-chatbot-public.php:95
msgid "AI Assistant"
msgstr ""

#: public/class-wp-rag-chatbot-public.php:96
msgid "Online"
msgstr ""

#: public/class-wp-rag-chatbot-public.php:99
msgid "Minimize"
msgstr ""

#: public/class-wp-rag-chatbot-public.php:105
msgid "Close"
msgstr ""

#: public/class-wp-rag-chatbot-public.php:125
msgid "Hello! How can I help you today?"
msgstr ""

#: public/class-wp-rag-chatbot-public.php:135
msgid "Type your message..."
msgstr ""

#: includes/class-wp-rag-chatbot-api.php:82
msgid "Security check failed."
msgstr ""

#: includes/class-wp-rag-chatbot-api.php:93
msgid "Unable to connect to AI backend."
msgstr ""

#: includes/class-wp-rag-chatbot-api.php:100
msgid "Invalid response from AI backend."
msgstr ""

#: includes/class-wp-rag-chatbot-api.php:107
msgid "AI backend error."
msgstr ""

#: includes/class-wp-rag-chatbot-api.php:195
msgid "Message cannot be empty."
msgstr ""

#: includes/class-wp-rag-chatbot-api.php:201
msgid "Message is too long (maximum 500 characters)."
msgstr ""

#: includes/class-wp-rag-chatbot-file-handler.php:58
msgid "Failed to move uploaded file."
msgstr ""

#: includes/class-wp-rag-chatbot-file-handler.php:72
msgid "PDF uploaded and processed successfully."
msgstr ""

#: includes/class-wp-rag-chatbot-file-handler.php:89
msgid "File not found."
msgstr ""

#: includes/class-wp-rag-chatbot-file-handler.php:99
msgid "File deleted successfully."
msgstr ""

#: includes/class-wp-rag-chatbot-file-handler.php:125
msgid "Only PDF files are allowed."
msgstr ""

#: includes/class-wp-rag-chatbot-file-handler.php:137
msgid "Invalid PDF file."
msgstr ""

#: includes/class-wp-rag-chatbot-file-handler.php:144
#, php-format
msgid "File size exceeds %s limit."
msgstr ""

#: includes/class-wp-rag-chatbot-file-handler.php:207
msgid "Failed to connect to AI backend."
msgstr ""

#: includes/class-wp-rag-chatbot-file-handler.php:220
msgid "Invalid response from AI backend."
msgstr ""

#: includes/class-wp-rag-chatbot-file-handler.php:227
msgid "Backend processing failed."
msgstr ""

#: includes/class-wp-rag-chatbot-file-handler.php:252
msgid "File is too large."
msgstr ""

#: includes/class-wp-rag-chatbot-file-handler.php:255
msgid "File was only partially uploaded."
msgstr ""

#: includes/class-wp-rag-chatbot-file-handler.php:257
msgid "No file was uploaded."
msgstr ""

#: includes/class-wp-rag-chatbot-file-handler.php:259
msgid "Missing temporary folder."
msgstr ""

#: includes/class-wp-rag-chatbot-file-handler.php:261
msgid "Failed to write file to disk."
msgstr ""

#: includes/class-wp-rag-chatbot-file-handler.php:263
msgid "File upload stopped by extension."
msgstr ""

#: includes/class-wp-rag-chatbot-file-handler.php:265
msgid "Unknown upload error."
msgstr ""

#: includes/class-wp-rag-chatbot-settings.php:244
msgid "Invalid API endpoint URL."
msgstr ""

#: includes/class-wp-rag-chatbot-settings.php:254
#, php-format
msgid "Connection failed: %s"
msgstr ""

#: includes/class-wp-rag-chatbot-settings.php:260
#, php-format
msgid "API returned status code: %d"
msgstr ""

#: includes/class-wp-rag-chatbot-settings.php:268
msgid "Invalid response from API."
msgstr ""

#: includes/class-wp-rag-chatbot-settings.php:273
msgid "API connection successful."
msgstr ""
