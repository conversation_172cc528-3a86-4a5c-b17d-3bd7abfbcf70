#!/usr/bin/env python3
"""
RAG Query Processor for WP RAG Chatbot
Handles user queries using Retrieval-Augmented Generation
"""

import sys
import json
import os

def load_arguments():
    """Load arguments from JSON file"""
    if len(sys.argv) != 2:
        raise ValueError("Usage: python rag_query.py <args_file>")
    
    args_file = sys.argv[1]
    with open(args_file, 'r') as f:
        return json.load(f)

def setup_environment(args):
    """Setup environment variables"""
    os.environ['OPENAI_API_KEY'] = args.get('openai_key', '')
    os.environ['PINECONE_API_KEY'] = args.get('pinecone_key', '')

def initialize_pinecone(api_key, index_name="rag-index"):
    """Initialize Pinecone client"""
    try:
        from pinecone import Pinecone
        
        pc = Pinecone(api_key=api_key)
        return pc
    except Exception as e:
        raise Exception(f"Error initializing Pinecone: {str(e)}")

def initialize_embeddings(api_key):
    """Initialize OpenAI embeddings"""
    try:
        from langchain_openai import OpenAIEmbeddings
        
        embeddings = OpenAIEmbeddings(
            model="text-embedding-3-small",
            openai_api_key=api_key
        )
        return embeddings
    except Exception as e:
        raise Exception(f"Error initializing embeddings: {str(e)}")

def initialize_llm(api_key):
    """Initialize ChatOpenAI LLM"""
    try:
        from langchain_openai import ChatOpenAI
        
        llm = ChatOpenAI(
            model_name="gpt-4o-mini",
            openai_api_key=api_key,
            temperature=0.7
        )
        return llm
    except Exception as e:
        raise Exception(f"Error initializing LLM: {str(e)}")

def create_rag_prompt_template():
    """Create RAG prompt template"""
    try:
        from langchain_core.prompts import ChatPromptTemplate
        
        template = """
You are a helpful assistant. Use only the following context to answer the user's question.
If the answer cannot be found in the context, respond with "I don't know based on the provided information."

Context:
{context}

User Question:
{query}

Answer:
"""
        return ChatPromptTemplate.from_template(template)
    except Exception as e:
        raise Exception(f"Error creating prompt template: {str(e)}")

def query_llm_with_rag(query, vector_store, llm, top_k=5):
    """Query LLM with RAG"""
    try:
        from langchain_core.output_parsers import StrOutputParser
        
        # Retrieve relevant chunks
        retriever = vector_store.as_retriever(search_kwargs={"k": top_k})
        retrieved_docs = retriever.get_relevant_documents(query)

        context = "\n\n".join([doc.page_content for doc in retrieved_docs]) if retrieved_docs else "No relevant context found."
        
        # Create prompt and chain
        prompt_template = create_rag_prompt_template()
        chain = prompt_template | llm | StrOutputParser()
        
        # Run the chain
        response = chain.invoke({"query": query, "context": context})
        return response.strip()
    except Exception as e:
        return f"Error querying LLM: {str(e)}"

def main():
    """Main query function"""
    try:
        # Load arguments
        args = load_arguments()
        
        # Setup environment
        setup_environment(args)
        
        question = args['question']
        
        # Initialize components
        pc = initialize_pinecone(args['pinecone_key'])
        embedding_function = initialize_embeddings(args['openai_key'])
        llm = initialize_llm(args['openai_key'])
        
        # Initialize vector store
        from langchain_pinecone import PineconeVectorStore
        vector_store = PineconeVectorStore(
            index_name="rag-index",
            embedding=embedding_function
        )
        
        # Query the RAG system
        answer = query_llm_with_rag(question, vector_store, llm)
        
        return {
            'success': True,
            'question': question,
            'answer': answer
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

if __name__ == "__main__":
    try:
        result = main()
        print(json.dumps(result))
    except Exception as e:
        error_result = {
            'success': False,
            'error': str(e)
        }
        print(json.dumps(error_result))
