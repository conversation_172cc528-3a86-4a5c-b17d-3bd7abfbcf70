#!/usr/bin/env python3
"""
PDF Processor for WP RAG Chatbot
Processes PDF files and stores them in Pinecone vector database
"""

import sys
import json
import os
import hashlib
import tempfile
from pathlib import Path

def load_arguments():
    """Load arguments from JSON file"""
    if len(sys.argv) != 2:
        raise ValueError("Usage: python pdf_processor.py <args_file>")
    
    args_file = sys.argv[1]
    with open(args_file, 'r') as f:
        return json.load(f)

def setup_environment(args):
    """Setup environment variables"""
    os.environ['OPENAI_API_KEY'] = args.get('openai_key', '')
    os.environ['PINECONE_API_KEY'] = args.get('pinecone_key', '')

def validate_pdf(file_path):
    """Validate PDF file"""
    try:
        import fitz  # PyMuPDF
        
        doc = fitz.open(file_path)
        page_count = len(doc)
        
        if page_count > 5:
            return False, f"PDF has {page_count} pages. Maximum allowed is 5."

        full_text = ""
        for page in doc:
            full_text += page.get_text()

        word_count = len(full_text.split())

        if word_count > 10000:
            return False, f"PDF has {word_count} words. Maximum allowed is 10,000."

        return True, "PDF is valid."
    
    except Exception as e:
        return False, f"Error reading PDF: {str(e)}"

def process_pdf_and_split(file_path, chunk_size=500, chunk_overlap=50):
    """Process PDF and split into chunks"""
    try:
        import fitz  # PyMuPDF
        from langchain.text_splitter import RecursiveCharacterTextSplitter
        
        # Read PDF with PyMuPDF
        doc = fitz.open(file_path)
        full_text = ""
        for page in doc:
            full_text += page.get_text()

        # Split using LangChain's RecursiveCharacterTextSplitter
        splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            separators=["\n\n", "\n", ".", "!", "?"]
        )

        chunks = splitter.split_text(full_text)
        return chunks
    except Exception as e:
        raise ValueError(f"Error processing PDF: {str(e)}")

def get_pdf_hash(file_path):
    """Get PDF file hash"""
    with open(file_path, 'rb') as f:
        file_bytes = f.read()
    return hashlib.sha256(file_bytes).hexdigest()

def initialize_pinecone(api_key, index_name="rag-index"):
    """Initialize Pinecone client and create index if needed"""
    try:
        from pinecone import Pinecone, ServerlessSpec
        
        pc = Pinecone(api_key=api_key)
        if index_name not in pc.list_indexes().names():
            pc.create_index(
                name=index_name,
                dimension=1536,
                metric="cosine",
                spec=ServerlessSpec(cloud="aws", region="us-east-1")
            )
        return pc
    except Exception as e:
        raise Exception(f"Error initializing Pinecone: {str(e)}")

def initialize_embeddings(api_key):
    """Initialize OpenAI embeddings"""
    try:
        from langchain_openai import OpenAIEmbeddings
        
        embeddings = OpenAIEmbeddings(
            model="text-embedding-3-small",
            openai_api_key=api_key
        )
        return embeddings
    except Exception as e:
        raise Exception(f"Error initializing embeddings: {str(e)}")

def is_document_already_indexed(index, pdf_hash):
    """Check if document is already indexed"""
    try:
        results = index.query(
            vector=[0.0] * 1536,
            top_k=1,
            filter={"doc_hash": {"$eq": pdf_hash}}
        )
        return len(results.matches) > 0
    except Exception as e:
        return False

def store_chunks_in_pinecone(chunks, embedding_function, pdf_hash, index_name="rag-index"):
    """Store chunks in Pinecone"""
    try:
        from langchain_pinecone import PineconeVectorStore
        
        metadatas = [{"doc_hash": pdf_hash, "chunk_id": i} for i in range(len(chunks))]
        vector_store = PineconeVectorStore.from_texts(
            texts=chunks,
            embedding=embedding_function,
            index_name=index_name,
            metadatas=metadatas
        )
        return vector_store
    except Exception as e:
        raise Exception(f"Error storing chunks in Pinecone: {str(e)}")

def main():
    """Main processing function"""
    try:
        # Load arguments
        args = load_arguments()
        
        # Setup environment
        setup_environment(args)
        
        file_path = args['file_path']
        filename = args['filename']
        
        # Validate PDF
        is_valid, message = validate_pdf(file_path)
        if not is_valid:
            return {
                'success': False,
                'error': message
            }
        
        # Get PDF hash
        pdf_hash = get_pdf_hash(file_path)
        
        # Initialize Pinecone
        pc = initialize_pinecone(args['pinecone_key'])
        index = pc.Index("rag-index")
        
        # Check if already indexed
        if is_document_already_indexed(index, pdf_hash):
            return {
                'success': True,
                'pdf_hash': pdf_hash,
                'chunks_count': 0,
                'message': 'Document already indexed'
            }
        
        # Process PDF and split into chunks
        chunks = process_pdf_and_split(file_path)
        
        # Initialize embeddings
        embedding_function = initialize_embeddings(args['openai_key'])
        
        # Store chunks in Pinecone
        store_chunks_in_pinecone(chunks, embedding_function, pdf_hash)
        
        return {
            'success': True,
            'pdf_hash': pdf_hash,
            'chunks_count': len(chunks),
            'message': 'PDF processed and indexed successfully'
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

if __name__ == "__main__":
    try:
        result = main()
        print(json.dumps(result))
    except Exception as e:
        error_result = {
            'success': False,
            'error': str(e)
        }
        print(json.dumps(error_result))
