<?php
/**
 * Public functionality for WP RAG Chatbot
 *
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WP RAG Chatbot Public Class
 *
 * @since 1.0.0
 */
class WP_RAG_Chatbot_Public {

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_action('wp_footer', array($this, 'render_chat_interface'));
    }

    /**
     * Enqueue public scripts and styles
     *
     * @since 1.0.0
     */
    public function enqueue_scripts() {
        // Only load on frontend if chat is enabled
        if (!WP_RAG_Chatbot::get_option('chat_enabled', true)) {
            return;
        }

        wp_enqueue_style(
            'wp-rag-chatbot-public',
            WP_RAG_CHATBOT_PLUGIN_URL . 'assets/css/public.css',
            array(),
            WP_RAG_CHATBOT_VERSION
        );

        wp_enqueue_script(
            'wp-rag-chatbot-public',
            WP_RAG_CHATBOT_PLUGIN_URL . 'assets/js/public.js',
            array('jquery'),
            WP_RAG_CHATBOT_VERSION,
            true
        );

        // Localize script with AJAX data
        wp_localize_script('wp-rag-chatbot-public', 'wpRagChatbot', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'rest_url' => rest_url('wp-rag-chatbot/v1/'),
            'nonce' => wp_create_nonce('wp_rag_chatbot_nonce'),
            'position' => WP_RAG_Chatbot::get_option('chat_position', 'bottom-left'),
            'strings' => array(
                'welcome_message' => __('Hello! How can I help you today?', 'wp-rag-chatbot'),
                'placeholder' => __('Type your message...', 'wp-rag-chatbot'),
                'send_button' => __('Send', 'wp-rag-chatbot'),
                'error_message' => __('Sorry, something went wrong. Please try again.', 'wp-rag-chatbot'),
                'thinking' => __('Thinking...', 'wp-rag-chatbot'),
                'close' => __('Close', 'wp-rag-chatbot'),
                'minimize' => __('Minimize', 'wp-rag-chatbot')
            )
        ));
    }

    /**
     * Render chat interface in footer
     *
     * @since 1.0.0
     */
    public function render_chat_interface() {
        // Only render if chat is enabled
        if (!WP_RAG_Chatbot::get_option('chat_enabled', true)) {
            return;
        }

        $position = WP_RAG_Chatbot::get_option('chat_position', 'bottom-left');
        ?>
        <div id="wp-rag-chatbot-container" class="wp-rag-chatbot-container position-<?php echo esc_attr($position); ?>">
            <!-- Chat Toggle Button -->
            <div id="wp-rag-chatbot-toggle" class="wp-rag-chatbot-toggle">
                <svg class="chat-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M20 2H4C2.9 2 2 2.9 2 4V22L6 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2ZM20 16H5.17L4 17.17V4H20V16Z" fill="currentColor"/>
                    <path d="M7 9H17V11H7V9ZM7 12H15V14H7V12Z" fill="currentColor"/>
                </svg>
                <svg class="close-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="display: none;">
                    <path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z" fill="currentColor"/>
                </svg>
            </div>

            <!-- Chat Window -->
            <div id="wp-rag-chatbot-window" class="wp-rag-chatbot-window" style="display: none;">
                <!-- Chat Header -->
                <div class="wp-rag-chatbot-header">
                    <div class="header-content">
                        <div class="bot-avatar">
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V19C3 20.1 3.9 21 5 21H11V19H5V3H13V9H21Z" fill="currentColor"/>
                            </svg>
                        </div>
                        <div class="bot-info">
                            <h4><?php _e('AI Assistant', 'wp-rag-chatbot'); ?></h4>
                            <span class="status"><?php _e('Online', 'wp-rag-chatbot'); ?></span>
                        </div>
                    </div>
                    <div class="header-actions">
                        <button id="wp-rag-chatbot-minimize" class="header-btn" title="<?php esc_attr_e('Minimize', 'wp-rag-chatbot'); ?>">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M19 13H5V11H19V13Z" fill="currentColor"/>
                            </svg>
                        </button>
                        <button id="wp-rag-chatbot-close" class="header-btn" title="<?php esc_attr_e('Close', 'wp-rag-chatbot'); ?>">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M19 6.41L17.59 5L12 10.59L6.41 5L5 6.41L10.59 12L5 17.59L6.41 19L12 13.41L17.59 19L19 17.59L13.41 12L19 6.41Z" fill="currentColor"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Chat Messages Area -->
                <div class="wp-rag-chatbot-messages" id="wp-rag-chatbot-messages">
                    <div class="message bot-message">
                        <div class="message-avatar">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z" fill="currentColor"/>
                            </svg>
                        </div>
                        <div class="message-content">
                            <div class="message-text">
                                <?php _e('Hello! How can I help you today?', 'wp-rag-chatbot'); ?>
                            </div>
                            <div class="message-time">
                                <?php echo esc_html(current_time('H:i')); ?>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chat Input Area -->
                <div class="wp-rag-chatbot-input">
                    <form id="wp-rag-chatbot-form">
                        <div class="input-group">
                            <input 
                                type="text" 
                                id="wp-rag-chatbot-message" 
                                placeholder="<?php esc_attr_e('Type your message...', 'wp-rag-chatbot'); ?>"
                                maxlength="500"
                                autocomplete="off"
                            >
                            <button type="submit" id="wp-rag-chatbot-send" class="send-button">
                                <svg class="send-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M2.01 21L23 12L2.01 3L2 10L17 12L2 14L2.01 21Z" fill="currentColor"/>
                                </svg>
                                <svg class="loading-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="display: none;">
                                    <circle cx="12" cy="12" r="3" fill="currentColor">
                                        <animate attributeName="r" values="3;6;3" dur="1s" repeatCount="indefinite"/>
                                        <animate attributeName="opacity" values="1;0.5;1" dur="1s" repeatCount="indefinite"/>
                                    </circle>
                                </svg>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <style>
        /* Inline critical CSS for immediate rendering */
        .wp-rag-chatbot-container {
            position: fixed;
            z-index: 999999;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .wp-rag-chatbot-container.position-bottom-left {
            bottom: 20px;
            left: 20px;
        }
        .wp-rag-chatbot-container.position-bottom-right {
            bottom: 20px;
            right: 20px;
        }
        .wp-rag-chatbot-container.position-top-left {
            top: 20px;
            left: 20px;
        }
        .wp-rag-chatbot-container.position-top-right {
            top: 20px;
            right: 20px;
        }
        .wp-rag-chatbot-toggle {
            width: 60px;
            height: 60px;
            background: #007cba;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }
        .wp-rag-chatbot-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }
        .wp-rag-chatbot-window {
            width: 350px;
            height: 500px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            position: absolute;
            bottom: 80px;
            right: 0;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }
        @media (max-width: 480px) {
            .wp-rag-chatbot-window {
                width: calc(100vw - 40px);
                height: calc(100vh - 120px);
                position: fixed;
                bottom: 20px;
                left: 20px;
                right: 20px;
            }
        }
        </style>
        <?php
    }
}
