<?php
/**
 * API-Only implementation for WP RAG Chatbot
 * Uses direct API calls instead of Python scripts
 *
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WP RAG Chatbot API-Only Class
 *
 * @since 1.0.0
 */
class WP_RAG_Chatbot_API_Only {

    /**
     * Process PDF using direct API calls
     *
     * @param string $file_path Path to PDF file
     * @param string $filename Original filename
     * @return array Result array
     * @since 1.0.0
     */
    public function process_pdf($file_path, $filename) {
        try {
            // Extract text from PDF
            $text = $this->extract_pdf_text($file_path);
            
            if (!$text) {
                return array(
                    'success' => false,
                    'message' => __('Could not extract text from PDF.', 'wp-rag-chatbot')
                );
            }

            // Split text into chunks
            $chunks = $this->split_text_into_chunks($text);
            
            // Get embeddings from OpenAI
            $embeddings_result = $this->get_embeddings($chunks);
            
            if (!$embeddings_result['success']) {
                return $embeddings_result;
            }

            // Store in Pinecone
            $storage_result = $this->store_in_pinecone($chunks, $embeddings_result['embeddings'], $filename);
            
            return $storage_result;

        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => sprintf(__('Error processing PDF: %s', 'wp-rag-chatbot'), $e->getMessage())
            );
        }
    }

    /**
     * Query RAG system using direct API calls
     *
     * @param string $question User question
     * @return array Result array
     * @since 1.0.0
     */
    public function query_rag($question) {
        try {
            // Get question embedding
            $question_embedding = $this->get_single_embedding($question);
            
            if (!$question_embedding['success']) {
                return $question_embedding;
            }

            // Search Pinecone for relevant chunks
            $search_result = $this->search_pinecone($question_embedding['embedding']);
            
            if (!$search_result['success']) {
                return $search_result;
            }

            // Generate answer using OpenAI
            $answer_result = $this->generate_answer($question, $search_result['context']);
            
            return $answer_result;

        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => sprintf(__('Error querying RAG: %s', 'wp-rag-chatbot'), $e->getMessage())
            );
        }
    }

    /**
     * Extract text from PDF (simple method)
     *
     * @param string $file_path PDF file path
     * @return string|false Extracted text or false on failure
     * @since 1.0.0
     */
    private function extract_pdf_text($file_path) {
        // Simple PDF text extraction using basic parsing
        $content = file_get_contents($file_path);
        
        // Basic PDF text extraction (works for simple PDFs)
        if (preg_match_all('/\(([^)]+)\)/', $content, $matches)) {
            return implode(' ', $matches[1]);
        }
        
        // Alternative method for different PDF formats
        if (preg_match_all('/BT\s*\/\w+\s+\d+\s+Tf\s*([^ET]+)ET/', $content, $matches)) {
            $text = '';
            foreach ($matches[1] as $match) {
                $text .= preg_replace('/\[[^\]]*\]/', '', $match);
            }
            return trim($text);
        }
        
        return false;
    }

    /**
     * Split text into chunks
     *
     * @param string $text Input text
     * @param int $chunk_size Chunk size
     * @param int $overlap Overlap size
     * @return array Text chunks
     * @since 1.0.0
     */
    private function split_text_into_chunks($text, $chunk_size = 500, $overlap = 50) {
        $words = explode(' ', $text);
        $chunks = array();
        
        for ($i = 0; $i < count($words); $i += ($chunk_size - $overlap)) {
            $chunk_words = array_slice($words, $i, $chunk_size);
            $chunk = implode(' ', $chunk_words);
            
            if (trim($chunk)) {
                $chunks[] = trim($chunk);
            }
        }
        
        return $chunks;
    }

    /**
     * Get embeddings from OpenAI
     *
     * @param array $texts Array of texts
     * @return array Result with embeddings
     * @since 1.0.0
     */
    private function get_embeddings($texts) {
        $settings = get_option('wp_rag_chatbot_settings', array());
        $api_key = $settings['openai_api_key'] ?? '';
        
        if (empty($api_key)) {
            return array(
                'success' => false,
                'message' => __('OpenAI API key not configured.', 'wp-rag-chatbot')
            );
        }

        $embeddings = array();
        
        foreach ($texts as $text) {
            $response = wp_remote_post('https://api.openai.com/v1/embeddings', array(
                'headers' => array(
                    'Authorization' => 'Bearer ' . $api_key,
                    'Content-Type' => 'application/json'
                ),
                'body' => json_encode(array(
                    'input' => $text,
                    'model' => 'text-embedding-3-small'
                )),
                'timeout' => 30
            ));

            if (is_wp_error($response)) {
                return array(
                    'success' => false,
                    'message' => __('Failed to get embeddings from OpenAI.', 'wp-rag-chatbot')
                );
            }

            $body = wp_remote_retrieve_body($response);
            $data = json_decode($body, true);

            if (!isset($data['data'][0]['embedding'])) {
                return array(
                    'success' => false,
                    'message' => __('Invalid response from OpenAI embeddings API.', 'wp-rag-chatbot')
                );
            }

            $embeddings[] = $data['data'][0]['embedding'];
        }

        return array(
            'success' => true,
            'embeddings' => $embeddings
        );
    }

    /**
     * Get single embedding from OpenAI
     *
     * @param string $text Input text
     * @return array Result with embedding
     * @since 1.0.0
     */
    private function get_single_embedding($text) {
        $result = $this->get_embeddings(array($text));
        
        if ($result['success']) {
            return array(
                'success' => true,
                'embedding' => $result['embeddings'][0]
            );
        }
        
        return $result;
    }

    /**
     * Store embeddings in Pinecone
     *
     * @param array $texts Text chunks
     * @param array $embeddings Embeddings
     * @param string $filename Source filename
     * @return array Result
     * @since 1.0.0
     */
    private function store_in_pinecone($texts, $embeddings, $filename) {
        $settings = get_option('wp_rag_chatbot_settings', array());
        $api_key = $settings['pinecone_api_key'] ?? '';
        
        if (empty($api_key)) {
            return array(
                'success' => false,
                'message' => __('Pinecone API key not configured.', 'wp-rag-chatbot')
            );
        }

        // Prepare vectors for Pinecone
        $vectors = array();
        for ($i = 0; $i < count($texts); $i++) {
            $vectors[] = array(
                'id' => $filename . '_chunk_' . $i,
                'values' => $embeddings[$i],
                'metadata' => array(
                    'text' => $texts[$i],
                    'source' => $filename
                )
            );
        }

        // Store in Pinecone (you'll need to implement Pinecone API calls)
        // For now, store locally as a fallback
        $stored_vectors = get_option('wp_rag_chatbot_vectors', array());
        $stored_vectors = array_merge($stored_vectors, $vectors);
        update_option('wp_rag_chatbot_vectors', $stored_vectors);

        return array(
            'success' => true,
            'message' => __('PDF processed and stored successfully.', 'wp-rag-chatbot'),
            'chunks_count' => count($texts)
        );
    }

    /**
     * Search for relevant context
     *
     * @param array $query_embedding Query embedding
     * @return array Search result
     * @since 1.0.0
     */
    private function search_pinecone($query_embedding) {
        // For now, use local storage as fallback
        $stored_vectors = get_option('wp_rag_chatbot_vectors', array());
        
        if (empty($stored_vectors)) {
            return array(
                'success' => false,
                'message' => __('No documents found. Please upload some PDFs first.', 'wp-rag-chatbot')
            );
        }

        // Simple similarity search (cosine similarity)
        $similarities = array();
        foreach ($stored_vectors as $vector) {
            $similarity = $this->cosine_similarity($query_embedding, $vector['values']);
            $similarities[] = array(
                'similarity' => $similarity,
                'text' => $vector['metadata']['text']
            );
        }

        // Sort by similarity
        usort($similarities, function($a, $b) {
            return $b['similarity'] <=> $a['similarity'];
        });

        // Get top 3 results
        $top_results = array_slice($similarities, 0, 3);
        $context = implode("\n\n", array_column($top_results, 'text'));

        return array(
            'success' => true,
            'context' => $context
        );
    }

    /**
     * Generate answer using OpenAI
     *
     * @param string $question User question
     * @param string $context Relevant context
     * @return array Answer result
     * @since 1.0.0
     */
    private function generate_answer($question, $context) {
        $settings = get_option('wp_rag_chatbot_settings', array());
        $api_key = $settings['openai_api_key'] ?? '';
        
        $prompt = "Based on the following context, answer the user's question. If the answer cannot be found in the context, say 'I don't know based on the provided information.'\n\nContext:\n{$context}\n\nQuestion: {$question}\n\nAnswer:";

        $response = wp_remote_post('https://api.openai.com/v1/chat/completions', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode(array(
                'model' => 'gpt-4o-mini',
                'messages' => array(
                    array('role' => 'user', 'content' => $prompt)
                ),
                'max_tokens' => 500,
                'temperature' => 0.7
            )),
            'timeout' => 30
        ));

        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'message' => __('Failed to get response from OpenAI.', 'wp-rag-chatbot')
            );
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (!isset($data['choices'][0]['message']['content'])) {
            return array(
                'success' => false,
                'message' => __('Invalid response from OpenAI chat API.', 'wp-rag-chatbot')
            );
        }

        return array(
            'success' => true,
            'answer' => trim($data['choices'][0]['message']['content'])
        );
    }

    /**
     * Calculate cosine similarity
     *
     * @param array $a Vector A
     * @param array $b Vector B
     * @return float Similarity score
     * @since 1.0.0
     */
    private function cosine_similarity($a, $b) {
        $dot_product = 0;
        $norm_a = 0;
        $norm_b = 0;
        
        for ($i = 0; $i < count($a); $i++) {
            $dot_product += $a[$i] * $b[$i];
            $norm_a += $a[$i] * $a[$i];
            $norm_b += $b[$i] * $b[$i];
        }
        
        return $dot_product / (sqrt($norm_a) * sqrt($norm_b));
    }
}
