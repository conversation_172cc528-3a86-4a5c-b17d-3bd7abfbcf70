/**
 * Admin styles for WP RAG Chatbot
 * 
 * @since 1.0.0
 */

/* Dashboard Layout */
.wp-rag-chatbot-dashboard {
    margin-top: 20px;
}

.dashboard-widgets {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.dashboard-widget {
    background: white;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.dashboard-widget h3 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
    color: #1d2327;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 10px;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
}

.stat-item {
    text-align: center;
    padding: 15px;
    background: #f6f7f7;
    border-radius: 4px;
    border-left: 4px solid #007cba;
}

.stat-number {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: #007cba;
    line-height: 1;
}

.stat-label {
    display: block;
    font-size: 12px;
    color: #646970;
    margin-top: 5px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Upload Form */
#wp-rag-chatbot-upload-form {
    margin-bottom: 20px;
}

#wp-rag-chatbot-upload-form p {
    margin-bottom: 15px;
}

#pdf-file {
    width: 100%;
    padding: 8px;
    border: 2px dashed #c3c4c7;
    border-radius: 4px;
    background: #f6f7f7;
    transition: border-color 0.3s ease;
}

#pdf-file:hover,
#pdf-file:focus {
    border-color: #007cba;
    background: white;
}

/* Progress Bar */
#upload-progress {
    margin: 15px 0;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #007cba, #005a87);
    width: 0%;
    transition: width 0.3s ease;
    animation: progressPulse 2s infinite;
}

@keyframes progressPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Upload Messages */
#upload-message {
    margin-top: 15px;
    padding: 10px;
    border-radius: 4px;
    display: none;
}

#upload-message.success {
    background: #d1eddb;
    border-left: 4px solid #00a32a;
    color: #00a32a;
}

#upload-message.error {
    background: #f8d7da;
    border-left: 4px solid #d63384;
    color: #d63384;
}

/* Recent Files List */
.recent-files-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.recent-files-list li {
    padding: 12px 0;
    border-bottom: 1px solid #e0e0e0;
}

.recent-files-list li:last-child {
    border-bottom: none;
}

.recent-files-list strong {
    color: #1d2327;
    font-weight: 600;
}

.recent-files-list small {
    color: #646970;
    font-size: 12px;
}

/* Files Table */
.wp-rag-chatbot-files {
    background: white;
    border: 1px solid #c3c4c7;
    border-radius: 4px;
    overflow: hidden;
}

.wp-rag-chatbot-files .wp-list-table {
    border: none;
    box-shadow: none;
}

.wp-rag-chatbot-files .wp-list-table th,
.wp-rag-chatbot-files .wp-list-table td {
    padding: 12px 15px;
    vertical-align: middle;
}

.wp-rag-chatbot-files .wp-list-table th {
    background: #f6f7f7;
    font-weight: 600;
    color: #1d2327;
    border-bottom: 1px solid #c3c4c7;
}

/* Status Badges */
.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.status-processed {
    background: #d1eddb;
    color: #00a32a;
}

.status-badge.status-processing {
    background: #fff3cd;
    color: #856404;
}

.status-badge.status-error {
    background: #f8d7da;
    color: #d63384;
}

.status-badge.status-pending {
    background: #cce7ff;
    color: #0073aa;
}

/* Action Buttons */
.delete-file {
    background: #d63384;
    border-color: #d63384;
    color: white;
    transition: all 0.2s ease;
}

.delete-file:hover {
    background: #b02a5b;
    border-color: #b02a5b;
    color: white;
}

/* Settings Form */
.form-table th {
    width: 200px;
    font-weight: 600;
}

.form-table td {
    padding: 15px 10px;
}

.form-table input[type="url"],
.form-table input[type="text"],
.form-table input[type="number"] {
    width: 100%;
    max-width: 400px;
}

.form-table select {
    min-width: 200px;
}

.form-table .description {
    margin-top: 5px;
    font-style: italic;
    color: #646970;
}

/* API Connection Test */
.api-test-button {
    margin-left: 10px;
    vertical-align: top;
}

.api-test-result {
    margin-top: 10px;
    padding: 10px;
    border-radius: 4px;
    display: none;
}

.api-test-result.success {
    background: #d1eddb;
    border-left: 4px solid #00a32a;
    color: #00a32a;
}

.api-test-result.error {
    background: #f8d7da;
    border-left: 4px solid #d63384;
    color: #d63384;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007cba;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 782px) {
    .dashboard-widgets {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .wp-rag-chatbot-files {
        overflow-x: auto;
    }
    
    .wp-rag-chatbot-files .wp-list-table {
        min-width: 600px;
    }
}

@media (max-width: 480px) {
    .dashboard-widget {
        padding: 15px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-item {
        padding: 12px;
    }
    
    .form-table th,
    .form-table td {
        display: block;
        width: 100%;
        padding: 10px 0;
    }
    
    .form-table th {
        border-bottom: none;
        padding-bottom: 5px;
    }
}

/* Accessibility */
.screen-reader-text {
    clip: rect(1px, 1px, 1px, 1px);
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
}

/* Focus States */
button:focus,
input:focus,
select:focus,
textarea:focus {
    outline: 2px solid #007cba;
    outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .dashboard-widget {
        border-width: 2px;
    }
    
    .status-badge {
        border: 1px solid currentColor;
    }
    
    .progress-fill {
        background: #000;
    }
}
