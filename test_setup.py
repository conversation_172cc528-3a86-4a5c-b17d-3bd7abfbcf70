#!/usr/bin/env python3
"""
Test script for WP RAG Chatbot setup
Verifies that the Flask API backend is working correctly
"""

import requests
import json
import os
import sys
from pathlib import Path

def test_flask_api():
    """Test the Flask API endpoints"""
    base_url = "http://localhost:5000"
    
    print("🧪 Testing WP RAG Chatbot Flask API...")
    print("=" * 50)
    
    # Test 1: Health check
    print("1. Testing health endpoint...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("   ✅ Health check passed")
        else:
            print(f"   ❌ Health check failed: {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Health check failed: {e}")
        return False
    
    # Test 2: Status endpoint
    print("2. Testing status endpoint...")
    try:
        response = requests.get(f"{base_url}/status", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("   ✅ Status endpoint working")
                if 'status' in data:
                    status = data['status']
                    print(f"   📊 Total vectors: {status.get('total_vectors', 'N/A')}")
                    print(f"   📊 Index fullness: {status.get('index_fullness', 'N/A')}")
            else:
                print("   ⚠️  Status endpoint returned error")
        else:
            print(f"   ❌ Status endpoint failed: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Status endpoint failed: {e}")
    
    # Test 3: Query endpoint (without documents)
    print("3. Testing query endpoint...")
    try:
        test_query = {
            "question": "Hello, can you help me?"
        }
        response = requests.post(
            f"{base_url}/query", 
            json=test_query,
            timeout=30
        )
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("   ✅ Query endpoint working")
                print(f"   💬 Response: {data.get('answer', 'No answer')[:100]}...")
            else:
                print(f"   ⚠️  Query returned: {data.get('message', 'Unknown error')}")
        else:
            print(f"   ❌ Query endpoint failed: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"   ❌ Query endpoint failed: {e}")
    
    return True

def check_environment():
    """Check if environment is properly set up"""
    print("\n🔧 Checking environment setup...")
    print("=" * 50)
    
    # Check if .env file exists
    env_file = Path(".env")
    if env_file.exists():
        print("   ✅ .env file found")
    else:
        print("   ❌ .env file not found")
        print("   📝 Create a .env file with:")
        print("      OPENAI_API_KEY=your_key_here")
        print("      PINECONE_API_KEY=your_key_here")
        return False
    
    # Check environment variables
    required_vars = ["OPENAI_API_KEY", "PINECONE_API_KEY"]
    missing_vars = []
    
    # Load .env file
    try:
        with open(".env", "r") as f:
            for line in f:
                if "=" in line and not line.startswith("#"):
                    key, value = line.strip().split("=", 1)
                    os.environ[key] = value
    except Exception as e:
        print(f"   ❌ Error reading .env file: {e}")
        return False
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"   ❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    else:
        print("   ✅ All required environment variables found")
    
    # Check Python dependencies
    print("\n📦 Checking Python dependencies...")
    required_packages = [
        "flask", "flask-cors", "python-dotenv", "pinecone", 
        "langchain", "langchain-openai", "langchain-pinecone", 
        "openai", "requests", "PyMuPDF"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"   ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"   ❌ {package}")
    
    if missing_packages:
        print(f"\n   📥 Install missing packages with:")
        print(f"   pip install {' '.join(missing_packages)}")
        return False
    
    return True

def test_wordpress_integration():
    """Test WordPress integration (if WordPress is running)"""
    print("\n🌐 Testing WordPress integration...")
    print("=" * 50)
    
    # This would need to be customized based on your WordPress setup
    wordpress_url = "http://localhost/wordpress"  # Adjust as needed
    
    try:
        # Test WordPress REST API
        response = requests.get(f"{wordpress_url}/wp-json/wp-rag-chatbot/v1/health", timeout=5)
        if response.status_code == 200:
            print("   ✅ WordPress REST API accessible")
        else:
            print(f"   ⚠️  WordPress REST API returned: {response.status_code}")
    except requests.exceptions.RequestException:
        print("   ⚠️  WordPress not accessible (this is OK if not running locally)")
    
    return True

def create_sample_env():
    """Create a sample .env file"""
    sample_content = """# WP RAG Chatbot Environment Variables
# Copy this file to .env and fill in your actual API keys

# OpenAI API Key (get from https://platform.openai.com/api-keys)
OPENAI_API_KEY=your_openai_api_key_here

# Pinecone API Key (get from https://app.pinecone.io/)
PINECONE_API_KEY=your_pinecone_api_key_here

# Optional: Customize these settings
# PINECONE_ENVIRONMENT=us-east-1-aws
# PINECONE_INDEX_NAME=rag-index
"""
    
    with open(".env.example", "w") as f:
        f.write(sample_content)
    
    print("📝 Created .env.example file")
    print("   Copy it to .env and add your API keys")

def main():
    """Main test function"""
    print("🚀 WP RAG Chatbot Setup Test")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("flask_api.py").exists():
        print("❌ flask_api.py not found. Please run this script from the project root directory.")
        sys.exit(1)
    
    # Create sample .env if it doesn't exist
    if not Path(".env").exists() and not Path(".env.example").exists():
        create_sample_env()
    
    # Run tests
    env_ok = check_environment()
    
    if env_ok:
        api_ok = test_flask_api()
        wp_ok = test_wordpress_integration()
        
        print("\n" + "=" * 50)
        if env_ok and api_ok:
            print("🎉 Setup test completed successfully!")
            print("\n📋 Next steps:")
            print("1. Install the WordPress plugin")
            print("2. Configure the API endpoint in WordPress admin")
            print("3. Upload some PDF files")
            print("4. Test the chat interface")
        else:
            print("⚠️  Some tests failed. Please check the errors above.")
    else:
        print("\n❌ Environment setup incomplete. Please fix the issues above.")

if __name__ == "__main__":
    main()
