#!/usr/bin/env python3
"""
Test Python environment for WP RAG Chatbot
Checks if all required packages are installed
"""

import sys
import json

def test_packages():
    """Test if all required packages are available"""
    required_packages = {
        'fitz': 'PyMuPDF',
        'langchain': 'langchain',
        'langchain_openai': 'langchain-openai',
        'langchain_pinecone': 'langchain-pinecone',
        'pinecone': 'pinecone-client',
        'openai': 'openai',
        'requests': 'requests'
    }
    
    results = {}
    missing_packages = []
    
    for package, pip_name in required_packages.items():
        try:
            __import__(package)
            results[package] = {'status': 'installed', 'pip_name': pip_name}
        except ImportError:
            results[package] = {'status': 'missing', 'pip_name': pip_name}
            missing_packages.append(pip_name)
    
    return results, missing_packages

def main():
    """Main test function"""
    try:
        # Test Python version
        python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
        
        # Test packages
        package_results, missing_packages = test_packages()
        
        result = {
            'python_version': python_version,
            'packages': package_results,
            'missing_packages': missing_packages,
            'all_packages_available': len(missing_packages) == 0
        }
        
        return result
        
    except Exception as e:
        return {
            'error': str(e)
        }

if __name__ == "__main__":
    try:
        result = main()
        print(json.dumps(result))
    except Exception as e:
        error_result = {
            'error': str(e)
        }
        print(json.dumps(error_result))
