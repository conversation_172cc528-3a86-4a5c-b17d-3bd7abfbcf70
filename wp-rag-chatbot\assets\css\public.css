/**
 * Public styles for WP RAG Chatbot
 * 
 * @since 1.0.0
 */

/* Chat Container */
.wp-rag-chatbot-container {
    position: fixed;
    z-index: 999999;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    font-size: 14px;
    line-height: 1.4;
}

/* Position variants */
.wp-rag-chatbot-container.position-bottom-left {
    bottom: 20px;
    left: 20px;
}

.wp-rag-chatbot-container.position-bottom-right {
    bottom: 20px;
    right: 20px;
}

.wp-rag-chatbot-container.position-top-left {
    top: 20px;
    left: 20px;
}

.wp-rag-chatbot-container.position-top-right {
    top: 20px;
    right: 20px;
}

/* Chat Toggle Button */
.wp-rag-chatbot-toggle {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #007cba 0%, #005a87 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 124, 186, 0.3);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    outline: none;
}

.wp-rag-chatbot-toggle:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0, 124, 186, 0.4);
}

.wp-rag-chatbot-toggle:active {
    transform: scale(0.95);
}

.wp-rag-chatbot-toggle svg {
    transition: all 0.3s ease;
}

/* Chat Window */
.wp-rag-chatbot-window {
    width: 350px;
    height: 500px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    position: absolute;
    bottom: 80px;
    right: 0;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    opacity: 0;
    transform: translateY(20px) scale(0.95);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.wp-rag-chatbot-window.show {
    opacity: 1;
    transform: translateY(0) scale(1);
}

/* Position adjustments for window */
.wp-rag-chatbot-container.position-bottom-left .wp-rag-chatbot-window,
.wp-rag-chatbot-container.position-top-left .wp-rag-chatbot-window {
    right: auto;
    left: 0;
}

.wp-rag-chatbot-container.position-top-left .wp-rag-chatbot-window,
.wp-rag-chatbot-container.position-top-right .wp-rag-chatbot-window {
    bottom: auto;
    top: 80px;
}

/* Chat Header */
.wp-rag-chatbot-header {
    background: linear-gradient(135deg, #007cba 0%, #005a87 100%);
    color: white;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 12px 12px 0 0;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 12px;
}

.bot-avatar {
    width: 32px;
    height: 32px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.bot-info h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
}

.bot-info .status {
    font-size: 12px;
    opacity: 0.8;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.header-btn {
    background: none;
    border: none;
    color: white;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.header-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* Messages Area */
.wp-rag-chatbot-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #f8f9fa;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.wp-rag-chatbot-messages::-webkit-scrollbar {
    width: 6px;
}

.wp-rag-chatbot-messages::-webkit-scrollbar-track {
    background: transparent;
}

.wp-rag-chatbot-messages::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

/* Message Styles */
.message {
    display: flex;
    gap: 12px;
    max-width: 85%;
    animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.user-message {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message-avatar {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.bot-message .message-avatar {
    background: #007cba;
    color: white;
}

.user-message .message-avatar {
    background: #e9ecef;
    color: #6c757d;
}

.message-content {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.message-text {
    background: white;
    padding: 12px 16px;
    border-radius: 18px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    word-wrap: break-word;
    line-height: 1.4;
}

.user-message .message-text {
    background: #007cba;
    color: white;
}

.message-time {
    font-size: 11px;
    color: #6c757d;
    padding: 0 8px;
}

.user-message .message-time {
    text-align: right;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 0;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    background: #007cba;
    border-radius: 50%;
    animation: typingBounce 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typingBounce {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Input Area */
.wp-rag-chatbot-input {
    padding: 16px 20px;
    background: white;
    border-top: 1px solid #e9ecef;
}

.input-group {
    display: flex;
    gap: 8px;
    align-items: flex-end;
}

#wp-rag-chatbot-message {
    flex: 1;
    border: 1px solid #e9ecef;
    border-radius: 20px;
    padding: 12px 16px;
    font-size: 14px;
    outline: none;
    resize: none;
    min-height: 20px;
    max-height: 100px;
    transition: border-color 0.2s ease;
    font-family: inherit;
}

#wp-rag-chatbot-message:focus {
    border-color: #007cba;
}

.send-button {
    width: 40px;
    height: 40px;
    background: #007cba;
    border: none;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.send-button:hover:not(:disabled) {
    background: #005a87;
    transform: scale(1.05);
}

.send-button:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

/* Mobile Responsive */
@media (max-width: 480px) {
    .wp-rag-chatbot-container {
        bottom: 10px !important;
        left: 10px !important;
        right: 10px !important;
        top: auto !important;
    }
    
    .wp-rag-chatbot-window {
        width: calc(100vw - 20px) !important;
        height: calc(100vh - 100px) !important;
        position: fixed !important;
        bottom: 80px !important;
        left: 10px !important;
        right: 10px !important;
        top: auto !important;
        max-height: 600px;
    }
    
    .wp-rag-chatbot-toggle {
        width: 50px;
        height: 50px;
    }
    
    .message {
        max-width: 90%;
    }
}

@media (max-width: 320px) {
    .wp-rag-chatbot-header {
        padding: 12px 16px;
    }
    
    .wp-rag-chatbot-messages {
        padding: 16px;
    }
    
    .wp-rag-chatbot-input {
        padding: 12px 16px;
    }
}
