# WP RAG Chatbot

A WordPress plugin that enables users to interact with a company-specific AI assistant using RAG (Retrieval-Augmented Generation) functionality with PDF document support.

## Features

- **Frontend Chat Interface**: Floating chat icon with responsive popup interface
- **PDF Document Upload**: Admin dashboard for uploading and managing PDF files
- **RAG Integration**: Connects to Python backend for document processing and AI responses
- **Secure File Handling**: Validates and securely stores PDF files
- **REST API**: Custom endpoints for chat functionality
- **Responsive Design**: Mobile-friendly chat interface
- **Internationalization**: Ready for translation with proper text domain
- **WordPress Standards**: Follows WordPress coding standards and best practices

## Requirements

- WordPress 6.0 or higher
- PHP 7.4 or higher
- Python backend server (Flask API included)
- OpenAI API key
- Pinecone API key

## Installation

### 1. Install WordPress Plugin

1. Download the plugin files
2. Upload the `wp-rag-chatbot` folder to `/wp-content/plugins/`
3. Activate the plugin through the 'Plugins' menu in WordPress

### 2. Set Up Python Backend

1. Navigate to the Picone directory (included with plugin)
2. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Create a `.env` file with your API keys:
   ```
   OPENAI_API_KEY=your_openai_api_key_here
   PINECONE_API_KEY=your_pinecone_api_key_here
   ```

4. Start the Flask API server:
   ```bash
   python flask_api.py
   ```

   The server will run on `http://localhost:5000` by default.

### 3. Configure WordPress Plugin

1. Go to **AI Chatbot** > **Settings** in WordPress admin
2. Set the API Endpoint to `http://localhost:5000` (or your server URL)
3. Configure other settings as needed
4. Test the API connection using the "Test Connection" button

## Usage

### Admin Dashboard

#### Uploading PDF Files

1. Navigate to **AI Chatbot** in the WordPress admin menu
2. Use the upload form to select a PDF file (max 5MB, 5 pages)
3. Click "Upload PDF" to process and index the document
4. View uploaded files in the **PDF Files** section

#### Managing Settings

- **API Endpoint**: URL of your Python Flask backend
- **Enable Chat**: Toggle chat interface on/off
- **Chat Position**: Choose where the chat icon appears (bottom-left, bottom-right, etc.)

### Frontend Chat

1. The chat icon appears on all frontend pages (when enabled)
2. Click the icon to open the chat interface
3. Type messages and receive AI-powered responses based on uploaded documents
4. Chat history is saved locally in the browser

## File Structure

```
wp-rag-chatbot/
├── wp-rag-chatbot.php          # Main plugin file
├── admin/                      # Admin dashboard functionality
│   └── class-wp-rag-chatbot-admin.php
├── public/                     # Frontend functionality
│   └── class-wp-rag-chatbot-public.php
├── includes/                   # Core functionality
│   ├── class-wp-rag-chatbot-api.php
│   ├── class-wp-rag-chatbot-file-handler.php
│   └── class-wp-rag-chatbot-settings.php
├── assets/                     # CSS and JavaScript
│   ├── css/
│   │   ├── admin.css
│   │   └── public.css
│   └── js/
│       ├── admin.js
│       └── public.js
├── languages/                  # Translation files
│   └── wp-rag-chatbot.pot
└── uploads/                    # PDF file storage
```

## API Endpoints

### WordPress REST API

- `GET /wp-json/wp-rag-chatbot/v1/health` - Health check
- `POST /wp-json/wp-rag-chatbot/v1/chat` - Send chat message
- `GET /wp-json/wp-rag-chatbot/v1/status` - Get system status (admin only)

### Python Flask API

- `GET /health` - Health check
- `POST /upload` - Upload and process PDF
- `POST /query` - Query the RAG system
- `GET /status` - Get system statistics

## Security Features

- **Nonce Verification**: All AJAX requests use WordPress nonces
- **User Capability Checks**: Admin functions require `manage_options` capability
- **File Validation**: Strict PDF file validation and size limits
- **Secure File Storage**: Files stored in protected directory with .htaccess
- **Input Sanitization**: All user inputs are properly sanitized

## Customization

### Styling

Modify the CSS files in `assets/css/` to customize the appearance:
- `public.css` - Frontend chat interface
- `admin.css` - Admin dashboard

### JavaScript

Extend functionality by modifying:
- `assets/js/public.js` - Frontend chat behavior
- `assets/js/admin.js` - Admin dashboard interactions

### Hooks and Filters

The plugin provides various WordPress hooks for customization:

```php
// Modify chat settings
add_filter('wp_rag_chatbot_settings', function($settings) {
    $settings['welcome_message'] = 'Custom welcome message';
    return $settings;
});

// Customize file upload validation
add_filter('wp_rag_chatbot_validate_file', function($is_valid, $file) {
    // Custom validation logic
    return $is_valid;
}, 10, 2);
```

## Troubleshooting

### Common Issues

1. **Chat not appearing**: Check if chat is enabled in settings
2. **API connection failed**: Verify Python backend is running and endpoint URL is correct
3. **File upload errors**: Check file size (max 5MB) and type (PDF only)
4. **Permission errors**: Ensure proper WordPress user capabilities

### Debug Mode

Enable WordPress debug mode to see detailed error messages:

```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

### Log Files

Check these locations for error logs:
- WordPress: `/wp-content/debug.log`
- Python backend: Console output where Flask is running

## Development

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

### Testing

Run the included test suite:

```bash
# WordPress plugin tests
wp-env start
npm run test

# Python backend tests
python -m pytest tests/
```

## License

This plugin is licensed under the GPL v2 or later.

## Support

For support and bug reports, please use the GitHub issues page or contact the development team.

## Changelog

### Version 1.0.0
- Initial release
- Frontend chat interface
- PDF upload and processing
- RAG integration with Python backend
- Admin dashboard
- REST API endpoints
- Security features
- Internationalization support
