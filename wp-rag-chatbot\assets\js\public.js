/**
 * Public JavaScript for WP RAG Chatbot
 * 
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Chat Bot Class
    class WPRagChatbot {
        constructor() {
            this.isOpen = false;
            this.isMinimized = false;
            this.isTyping = false;
            this.messageHistory = [];
            
            this.init();
        }

        init() {
            this.bindEvents();
            this.loadChatHistory();
        }

        bindEvents() {
            const $toggle = $('#wp-rag-chatbot-toggle');
            const $window = $('#wp-rag-chatbot-window');
            const $form = $('#wp-rag-chatbot-form');
            const $input = $('#wp-rag-chatbot-message');
            const $close = $('#wp-rag-chatbot-close');
            const $minimize = $('#wp-rag-chatbot-minimize');

            // Toggle chat window
            $toggle.on('click', () => this.toggleChat());

            // Close chat
            $close.on('click', () => this.closeChat());

            // Minimize chat
            $minimize.on('click', () => this.minimizeChat());

            // Form submission
            $form.on('submit', (e) => {
                e.preventDefault();
                this.sendMessage();
            });

            // Input events
            $input.on('keypress', (e) => {
                if (e.which === 13 && !e.shiftKey) {
                    e.preventDefault();
                    this.sendMessage();
                }
            });

            // Auto-resize input
            $input.on('input', () => this.autoResizeInput());

            // Click outside to close
            $(document).on('click', (e) => {
                if (this.isOpen && !$(e.target).closest('.wp-rag-chatbot-container').length) {
                    this.closeChat();
                }
            });

            // Escape key to close
            $(document).on('keydown', (e) => {
                if (e.key === 'Escape' && this.isOpen) {
                    this.closeChat();
                }
            });
        }

        toggleChat() {
            if (this.isOpen) {
                this.closeChat();
            } else {
                this.openChat();
            }
        }

        openChat() {
            const $window = $('#wp-rag-chatbot-window');
            const $toggle = $('#wp-rag-chatbot-toggle');
            
            this.isOpen = true;
            this.isMinimized = false;
            
            $window.show().addClass('show');
            $toggle.find('.chat-icon').hide();
            $toggle.find('.close-icon').show();
            
            // Focus input
            setTimeout(() => {
                $('#wp-rag-chatbot-message').focus();
            }, 300);

            // Scroll to bottom
            this.scrollToBottom();
        }

        closeChat() {
            const $window = $('#wp-rag-chatbot-window');
            const $toggle = $('#wp-rag-chatbot-toggle');
            
            this.isOpen = false;
            this.isMinimized = false;
            
            $window.removeClass('show');
            $toggle.find('.chat-icon').show();
            $toggle.find('.close-icon').hide();
            
            setTimeout(() => {
                $window.hide();
            }, 300);
        }

        minimizeChat() {
            this.closeChat();
        }

        async sendMessage() {
            const $input = $('#wp-rag-chatbot-message');
            const message = $input.val().trim();

            if (!message || this.isTyping) {
                return;
            }

            // Clear input
            $input.val('');
            this.autoResizeInput();

            // Add user message
            this.addMessage(message, 'user');

            // Show typing indicator
            this.showTypingIndicator();

            try {
                const response = await this.sendToAPI(message);
                
                // Hide typing indicator
                this.hideTypingIndicator();

                if (response.success) {
                    this.addMessage(response.response, 'bot');
                } else {
                    this.addMessage(wpRagChatbot.strings.error_message, 'bot', true);
                }
            } catch (error) {
                console.error('Chat error:', error);
                this.hideTypingIndicator();
                this.addMessage(wpRagChatbot.strings.error_message, 'bot', true);
            }
        }

        async sendToAPI(message) {
            const response = await fetch(wpRagChatbot.rest_url + 'chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': wpRagChatbot.nonce
                },
                body: JSON.stringify({
                    message: message,
                    nonce: wpRagChatbot.nonce
                })
            });

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            return await response.json();
        }

        addMessage(text, sender, isError = false) {
            const $messages = $('#wp-rag-chatbot-messages');
            const timestamp = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            
            const messageClass = sender === 'user' ? 'user-message' : 'bot-message';
            const avatarIcon = sender === 'user' ? 
                '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z" fill="currentColor"/></svg>' :
                '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z" fill="currentColor"/></svg>';

            const messageHtml = `
                <div class="message ${messageClass}">
                    <div class="message-avatar">
                        ${avatarIcon}
                    </div>
                    <div class="message-content">
                        <div class="message-text ${isError ? 'error' : ''}">${this.escapeHtml(text)}</div>
                        <div class="message-time">${timestamp}</div>
                    </div>
                </div>
            `;

            $messages.append(messageHtml);
            this.scrollToBottom();

            // Store in history
            this.messageHistory.push({
                text: text,
                sender: sender,
                timestamp: Date.now()
            });

            this.saveChatHistory();
        }

        showTypingIndicator() {
            const $messages = $('#wp-rag-chatbot-messages');
            this.isTyping = true;

            const typingHtml = `
                <div class="message bot-message typing-indicator" id="typing-indicator">
                    <div class="message-avatar">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <div class="message-content">
                        <div class="message-text">
                            <span>${wpRagChatbot.strings.thinking}</span>
                            <div class="typing-dots">
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                                <div class="typing-dot"></div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $messages.append(typingHtml);
            this.scrollToBottom();

            // Disable send button
            $('#wp-rag-chatbot-send').prop('disabled', true);
            $('#wp-rag-chatbot-send .send-icon').hide();
            $('#wp-rag-chatbot-send .loading-icon').show();
        }

        hideTypingIndicator() {
            $('#typing-indicator').remove();
            this.isTyping = false;

            // Enable send button
            $('#wp-rag-chatbot-send').prop('disabled', false);
            $('#wp-rag-chatbot-send .send-icon').show();
            $('#wp-rag-chatbot-send .loading-icon').hide();
        }

        scrollToBottom() {
            const $messages = $('#wp-rag-chatbot-messages');
            $messages.scrollTop($messages[0].scrollHeight);
        }

        autoResizeInput() {
            const $input = $('#wp-rag-chatbot-message');
            $input.css('height', 'auto');
            $input.css('height', Math.min($input[0].scrollHeight, 100) + 'px');
        }

        escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        loadChatHistory() {
            try {
                const history = localStorage.getItem('wp_rag_chatbot_history');
                if (history) {
                    this.messageHistory = JSON.parse(history);
                    
                    // Only load recent messages (last 10)
                    const recentMessages = this.messageHistory.slice(-10);
                    recentMessages.forEach(msg => {
                        if (msg.sender !== 'bot' || msg.text !== wpRagChatbot.strings.welcome_message) {
                            this.addMessageToDOM(msg.text, msg.sender);
                        }
                    });
                }
            } catch (error) {
                console.error('Error loading chat history:', error);
            }
        }

        saveChatHistory() {
            try {
                // Keep only last 50 messages
                const recentHistory = this.messageHistory.slice(-50);
                localStorage.setItem('wp_rag_chatbot_history', JSON.stringify(recentHistory));
            } catch (error) {
                console.error('Error saving chat history:', error);
            }
        }

        addMessageToDOM(text, sender) {
            const $messages = $('#wp-rag-chatbot-messages');
            const timestamp = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            
            const messageClass = sender === 'user' ? 'user-message' : 'bot-message';
            const avatarIcon = sender === 'user' ? 
                '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 12C14.21 12 16 10.21 16 8C16 5.79 14.21 4 12 4C9.79 4 8 5.79 8 8C8 10.21 9.79 12 12 12ZM12 14C9.33 14 4 15.34 4 18V20H20V18C20 15.34 14.67 14 12 14Z" fill="currentColor"/></svg>' :
                '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z" fill="currentColor"/></svg>';

            const messageHtml = `
                <div class="message ${messageClass}">
                    <div class="message-avatar">
                        ${avatarIcon}
                    </div>
                    <div class="message-content">
                        <div class="message-text">${this.escapeHtml(text)}</div>
                        <div class="message-time">${timestamp}</div>
                    </div>
                </div>
            `;

            // Insert before welcome message
            const $welcomeMessage = $messages.find('.message:first');
            if ($welcomeMessage.length) {
                $welcomeMessage.after(messageHtml);
            } else {
                $messages.append(messageHtml);
            }
        }
    }

    // Initialize when DOM is ready
    $(document).ready(function() {
        if (typeof wpRagChatbot !== 'undefined') {
            new WPRagChatbot();
        }
    });

})(jQuery);
