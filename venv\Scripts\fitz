#!C:\Users\<USER>\Desktop\Picone\venv\Scripts\python.exe
"""
Main execution script for fMRI analysis in the ecosystem.

"""
import os
import sys
import argparse
# import imp
import fitz
from fitz.tools.builder import new_workflow
from fitz.frontend import run, install


def main(arglist):
    """Main function for handing off execution from the command line."""
    args = parse_args(arglist)

    if args.func != fitz.tools.setup_project.main:
        check_env()

    # Call the function determined by the chosen subparser.
    args.func(args)


def parse_args(arglist):
    """Take an arglist and return an argparse Namespace."""
    parser = argparse.ArgumentParser()
    parser.add_argument('--verbose', '-v', action='store_true')
    subparsers = parser.add_subparsers()
    run_parser = fitz.tools.run_parser(subparsers)
    run_parser.set_defaults(func=run)

    setup_parser = subparsers.add_parser('setup', help='setup')
    setup_parser.description = 'setup a new fitz directory'
    setup_parser.set_defaults(func=fitz.tools.setup_project.main)

    install_parser = subparsers.add_parser('install', help='install workflows')
    install_parser.description = 'Install workflows requested by experiments'
    install_parser.set_defaults(func=install)

    new_parser = subparsers.add_parser('new', help='create new fitz things')
    new_parsers = new_parser.add_subparsers()
    new_workflow_parser = new_parsers.add_parser('workflow')
    new_workflow_parser.description = 'Create a new workflow skeleton.'
    new_workflow_parser.set_defaults(func=new_workflow)
    new_workflow_parser.add_argument('pipe_name', help='Name of pipeline')
    new_workflow_parser.add_argument('workflow_name', help='Name of workflow')

    return parser.parse_args(arglist)


def check_env():
    if 'FITZ_DIR' not in os.environ.keys():
        raise IOError("FITZ_DIR must be set. Run `export FITZ_DIR=<path>` " +
                      "to set it.")

if __name__ == '__main__':
    main(sys.argv[1:])
