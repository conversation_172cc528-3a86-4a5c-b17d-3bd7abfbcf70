#!/usr/bin/env python3
"""
Flask API Backend for WP RAG Chatbot
Provides REST endpoints for WordPress plugin integration
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import logging
import hashlib
from dotenv import load_dotenv
from pdf_utils import (
    load_environment, 
    initialize_pinecone, 
    initialize_embeddings, 
    initialize_llm,
    validate_pdf,
    process_pdf_and_split,
    store_chunks_in_pinecone,
    query_llm_with_rag,
    get_pdf_hash,
    is_document_already_indexed
)
from langchain_pinecone import PineconeVectorStore

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for WordPress integration

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global variables for initialized components
pc = None
embedding_function = None
llm = None
vector_store = None

def initialize_components():
    """Initialize all AI components"""
    global pc, embedding_function, llm, vector_store
    
    try:
        # Load environment variables
        PINECONE_API_KEY, OPENAI_API_KEY = load_environment()
        
        # Initialize Pinecone
        pc = initialize_pinecone(PINECONE_API_KEY)
        logger.info("Pinecone client initialized successfully")
        
        # Initialize embeddings
        embedding_function = initialize_embeddings(OPENAI_API_KEY)
        logger.info("Embeddings initialized successfully")
        
        # Initialize LLM
        llm = initialize_llm(OPENAI_API_KEY)
        logger.info("LLM initialized successfully")
        
        # Initialize PineconeVectorStore
        vector_store = PineconeVectorStore(
            index_name="rag-index",
            embedding=embedding_function
        )
        logger.info("Vector store initialized successfully")
        
        return True
    except Exception as e:
        logger.error(f"Error initializing components: {str(e)}")
        return False

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'message': 'Flask API is running'
    })

@app.route('/upload', methods=['POST'])
def upload_pdf():
    """Upload and process PDF file"""
    try:
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'message': 'No file provided'
            }), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'message': 'No file selected'
            }), 400
        
        if not file.filename.lower().endswith('.pdf'):
            return jsonify({
                'success': False,
                'message': 'Only PDF files are allowed'
            }), 400
        
        # Read file content
        file_content = file.read()
        
        # Get PDF hash
        pdf_hash = get_pdf_hash(file_content)
        
        # Check if document is already indexed
        if is_document_already_indexed(pc.Index("rag-index"), pdf_hash):
            return jsonify({
                'success': True,
                'message': 'Document already indexed',
                'pdf_hash': pdf_hash,
                'already_indexed': True
            })
        
        # Validate PDF
        is_valid, msg, extracted_text = validate_pdf(file_content)
        
        if not is_valid:
            return jsonify({
                'success': False,
                'message': msg
            }), 400
        
        # Process and split PDF
        chunks = process_pdf_and_split(file_content)
        
        # Store chunks in Pinecone
        store_chunks_in_pinecone(
            chunks=chunks,
            embedding_function=embedding_function,
            pdf_hash=pdf_hash
        )
        
        return jsonify({
            'success': True,
            'message': 'PDF processed and indexed successfully',
            'pdf_hash': pdf_hash,
            'chunks_count': len(chunks),
            'already_indexed': False
        })
        
    except Exception as e:
        logger.error(f"Error processing PDF: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error processing PDF: {str(e)}'
        }), 500

@app.route('/query', methods=['POST'])
def query_chatbot():
    """Query the RAG chatbot"""
    try:
        data = request.get_json()
        
        if not data or 'question' not in data:
            return jsonify({
                'success': False,
                'message': 'No question provided'
            }), 400
        
        question = data['question'].strip()
        
        if not question:
            return jsonify({
                'success': False,
                'message': 'Question cannot be empty'
            }), 400
        
        if len(question) > 500:
            return jsonify({
                'success': False,
                'message': 'Question too long (max 500 characters)'
            }), 400
        
        # Query the LLM with RAG
        answer = query_llm_with_rag(question, vector_store, llm)
        
        return jsonify({
            'success': True,
            'question': question,
            'answer': answer
        })
        
    except Exception as e:
        logger.error(f"Error querying chatbot: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error querying chatbot: {str(e)}'
        }), 500

@app.route('/status', methods=['GET'])
def get_status():
    """Get system status and statistics"""
    try:
        # Get index stats
        index = pc.Index("rag-index")
        stats = index.describe_index_stats()
        
        return jsonify({
            'success': True,
            'status': {
                'total_vectors': stats.total_vector_count,
                'index_fullness': stats.index_fullness,
                'dimension': stats.dimension
            }
        })
        
    except Exception as e:
        logger.error(f"Error getting status: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error getting status: {str(e)}'
        }), 500

if __name__ == '__main__':
    # Initialize components on startup
    if initialize_components():
        logger.info("Starting Flask API server...")
        app.run(host='0.0.0.0', port=5000, debug=True)
    else:
        logger.error("Failed to initialize components. Exiting.")
