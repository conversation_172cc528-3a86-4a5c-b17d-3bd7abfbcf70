<?php
/**
 * REST API functionality for WP RAG Chatbot
 *
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WP RAG Chatbot API Class
 *
 * @since 1.0.0
 */
class WP_RAG_Chatbot_API {

    /**
     * API namespace
     *
     * @var string
     * @since 1.0.0
     */
    private $namespace = 'wp-rag-chatbot/v1';

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        add_action('rest_api_init', array($this, 'register_routes'));
    }

    /**
     * Register REST API routes
     *
     * @since 1.0.0
     */
    public function register_routes() {
        // Chat endpoint
        register_rest_route($this->namespace, '/chat', array(
            'methods' => 'POST',
            'callback' => array($this, 'handle_chat_request'),
            'permission_callback' => array($this, 'check_chat_permissions'),
            'args' => array(
                'message' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field',
                    'validate_callback' => array($this, 'validate_message')
                ),
                'nonce' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field'
                )
            )
        ));

        // Health check endpoint
        register_rest_route($this->namespace, '/health', array(
            'methods' => 'GET',
            'callback' => array($this, 'health_check'),
            'permission_callback' => '__return_true'
        ));

        // Status endpoint
        register_rest_route($this->namespace, '/status', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_status'),
            'permission_callback' => array($this, 'check_admin_permissions')
        ));
    }

    /**
     * Handle chat request
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response|WP_Error Response object
     * @since 1.0.0
     */
    public function handle_chat_request($request) {
        $message = $request->get_param('message');
        $nonce = $request->get_param('nonce');

        // Verify nonce
        if (!wp_verify_nonce($nonce, 'wp_rag_chatbot_nonce')) {
            return new WP_Error(
                'invalid_nonce',
                __('Security check failed.', 'wp-rag-chatbot'),
                array('status' => 403)
            );
        }

        // Try Python bridge first, fallback to API-only approach
        require_once WP_RAG_CHATBOT_PLUGIN_PATH . 'includes/class-wp-rag-chatbot-python-bridge.php';
        $python_bridge = new WP_RAG_Chatbot_Python_Bridge();

        // Test if Python environment is available
        $python_test = $python_bridge->test_python_environment();

        if ($python_test['success'] && isset($python_test['details']['all_packages_available']) && $python_test['details']['all_packages_available']) {
            // Use Python bridge
            $result = $python_bridge->query_rag($message);
        } else {
            // Use API-only approach
            require_once WP_RAG_CHATBOT_PLUGIN_PATH . 'includes/class-wp-rag-chatbot-api-only.php';
            $api_processor = new WP_RAG_Chatbot_API_Only();
            $result = $api_processor->query_rag($message);
        }

        if (!$result['success']) {
            return new WP_Error(
                'backend_error',
                isset($result['message']) ? $result['message'] : __('AI backend error.', 'wp-rag-chatbot'),
                array('status' => 500)
            );
        }

        return rest_ensure_response(array(
            'success' => true,
            'message' => $message,
            'response' => $result['answer'],
            'timestamp' => current_time('timestamp')
        ));
    }

    /**
     * Health check endpoint
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     * @since 1.0.0
     */
    public function health_check($request) {
        // Test Python environment
        require_once WP_RAG_CHATBOT_PLUGIN_PATH . 'includes/class-wp-rag-chatbot-python-bridge.php';
        $python_bridge = new WP_RAG_Chatbot_Python_Bridge();

        $python_test = $python_bridge->test_python_environment();

        return rest_ensure_response(array(
            'wordpress' => true,
            'python' => $python_test['success'],
            'python_details' => $python_test,
            'timestamp' => current_time('timestamp')
        ));
    }

    /**
     * Get system status
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     * @since 1.0.0
     */
    public function get_status($request) {
        // Test Python environment
        require_once WP_RAG_CHATBOT_PLUGIN_PATH . 'includes/class-wp-rag-chatbot-python-bridge.php';
        $python_bridge = new WP_RAG_Chatbot_Python_Bridge();

        $python_test = $python_bridge->test_python_environment();

        // Get WordPress data
        $uploaded_files = get_option('wp_rag_chatbot_uploaded_files', array());

        return rest_ensure_response(array(
            'wordpress' => array(
                'uploaded_files' => count($uploaded_files),
                'settings' => get_option('wp_rag_chatbot_settings', array())
            ),
            'python' => $python_test,
            'timestamp' => current_time('timestamp')
        ));
    }



    /**
     * Check chat permissions
     *
     * @param WP_REST_Request $request Request object
     * @return bool True if user has permission
     * @since 1.0.0
     */
    public function check_chat_permissions($request) {
        // Allow all users to chat (you can modify this based on your needs)
        return true;
    }

    /**
     * Check admin permissions
     *
     * @param WP_REST_Request $request Request object
     * @return bool True if user has admin permission
     * @since 1.0.0
     */
    public function check_admin_permissions($request) {
        return current_user_can('manage_options');
    }

    /**
     * Validate chat message
     *
     * @param string $value Message value
     * @param WP_REST_Request $request Request object
     * @param string $param Parameter name
     * @return bool|WP_Error True if valid, error otherwise
     * @since 1.0.0
     */
    public function validate_message($value, $request, $param) {
        if (empty(trim($value))) {
            return new WP_Error(
                'empty_message',
                __('Message cannot be empty.', 'wp-rag-chatbot')
            );
        }

        if (strlen($value) > 500) {
            return new WP_Error(
                'message_too_long',
                __('Message is too long (maximum 500 characters).', 'wp-rag-chatbot')
            );
        }

        return true;
    }
}
