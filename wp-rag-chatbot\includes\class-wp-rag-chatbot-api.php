<?php
/**
 * REST API functionality for WP RAG Chatbot
 *
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WP RAG Chatbot API Class
 *
 * @since 1.0.0
 */
class WP_RAG_Chatbot_API {

    /**
     * API namespace
     *
     * @var string
     * @since 1.0.0
     */
    private $namespace = 'wp-rag-chatbot/v1';

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        add_action('rest_api_init', array($this, 'register_routes'));
    }

    /**
     * Register REST API routes
     *
     * @since 1.0.0
     */
    public function register_routes() {
        // Chat endpoint
        register_rest_route($this->namespace, '/chat', array(
            'methods' => 'POST',
            'callback' => array($this, 'handle_chat_request'),
            'permission_callback' => array($this, 'check_chat_permissions'),
            'args' => array(
                'message' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field',
                    'validate_callback' => array($this, 'validate_message')
                ),
                'nonce' => array(
                    'required' => true,
                    'type' => 'string',
                    'sanitize_callback' => 'sanitize_text_field'
                )
            )
        ));

        // Health check endpoint
        register_rest_route($this->namespace, '/health', array(
            'methods' => 'GET',
            'callback' => array($this, 'health_check'),
            'permission_callback' => '__return_true'
        ));

        // Status endpoint
        register_rest_route($this->namespace, '/status', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_status'),
            'permission_callback' => array($this, 'check_admin_permissions')
        ));
    }

    /**
     * Handle chat request
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response|WP_Error Response object
     * @since 1.0.0
     */
    public function handle_chat_request($request) {
        $message = $request->get_param('message');
        $nonce = $request->get_param('nonce');

        // Verify nonce
        if (!wp_verify_nonce($nonce, 'wp_rag_chatbot_nonce')) {
            return new WP_Error(
                'invalid_nonce',
                __('Security check failed.', 'wp-rag-chatbot'),
                array('status' => 403)
            );
        }

        // Get API endpoint from settings
        $api_endpoint = WP_RAG_Chatbot::get_option('api_endpoint', 'http://localhost:5000');
        
        // Prepare request to Python backend
        $response = $this->send_to_backend($api_endpoint . '/query', array(
            'question' => $message
        ));

        if (is_wp_error($response)) {
            return new WP_Error(
                'backend_error',
                __('Unable to connect to AI backend.', 'wp-rag-chatbot'),
                array('status' => 500)
            );
        }

        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);

        if (!$data || !isset($data['success'])) {
            return new WP_Error(
                'invalid_response',
                __('Invalid response from AI backend.', 'wp-rag-chatbot'),
                array('status' => 500)
            );
        }

        if (!$data['success']) {
            return new WP_Error(
                'backend_error',
                isset($data['message']) ? $data['message'] : __('AI backend error.', 'wp-rag-chatbot'),
                array('status' => 500)
            );
        }

        return rest_ensure_response(array(
            'success' => true,
            'message' => $message,
            'response' => $data['answer'],
            'timestamp' => current_time('timestamp')
        ));
    }

    /**
     * Health check endpoint
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     * @since 1.0.0
     */
    public function health_check($request) {
        $api_endpoint = WP_RAG_Chatbot::get_option('api_endpoint', 'http://localhost:5000');
        
        // Check if backend is reachable
        $response = wp_remote_get($api_endpoint . '/health', array(
            'timeout' => 5
        ));

        $backend_status = !is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200;

        return rest_ensure_response(array(
            'wordpress' => true,
            'backend' => $backend_status,
            'endpoint' => $api_endpoint,
            'timestamp' => current_time('timestamp')
        ));
    }

    /**
     * Get system status
     *
     * @param WP_REST_Request $request Request object
     * @return WP_REST_Response Response object
     * @since 1.0.0
     */
    public function get_status($request) {
        $api_endpoint = WP_RAG_Chatbot::get_option('api_endpoint', 'http://localhost:5000');
        
        // Get backend status
        $response = wp_remote_get($api_endpoint . '/status', array(
            'timeout' => 10
        ));

        $backend_data = array();
        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $body = wp_remote_retrieve_body($response);
            $backend_data = json_decode($body, true);
        }

        // Get WordPress data
        $uploaded_files = get_option('wp_rag_chatbot_uploaded_files', array());
        
        return rest_ensure_response(array(
            'wordpress' => array(
                'uploaded_files' => count($uploaded_files),
                'settings' => get_option('wp_rag_chatbot_settings', array())
            ),
            'backend' => $backend_data,
            'timestamp' => current_time('timestamp')
        ));
    }

    /**
     * Send request to Python backend
     *
     * @param string $url Backend URL
     * @param array $data Request data
     * @return array|WP_Error Response or error
     * @since 1.0.0
     */
    private function send_to_backend($url, $data) {
        $args = array(
            'method' => 'POST',
            'timeout' => 30,
            'headers' => array(
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode($data)
        );

        return wp_remote_request($url, $args);
    }

    /**
     * Check chat permissions
     *
     * @param WP_REST_Request $request Request object
     * @return bool True if user has permission
     * @since 1.0.0
     */
    public function check_chat_permissions($request) {
        // Allow all users to chat (you can modify this based on your needs)
        return true;
    }

    /**
     * Check admin permissions
     *
     * @param WP_REST_Request $request Request object
     * @return bool True if user has admin permission
     * @since 1.0.0
     */
    public function check_admin_permissions($request) {
        return current_user_can('manage_options');
    }

    /**
     * Validate chat message
     *
     * @param string $value Message value
     * @param WP_REST_Request $request Request object
     * @param string $param Parameter name
     * @return bool|WP_Error True if valid, error otherwise
     * @since 1.0.0
     */
    public function validate_message($value, $request, $param) {
        if (empty(trim($value))) {
            return new WP_Error(
                'empty_message',
                __('Message cannot be empty.', 'wp-rag-chatbot')
            );
        }

        if (strlen($value) > 500) {
            return new WP_Error(
                'message_too_long',
                __('Message is too long (maximum 500 characters).', 'wp-rag-chatbot')
            );
        }

        return true;
    }
}
