#!/usr/bin/env python3
"""
Simple Flask API test to verify basic connectivity
Run this first to test if Flask is working
"""

try:
    from flask import Flask, request, jsonify
    from flask_cors import CORS
    print("✅ Flask imports successful")
except ImportError as e:
    print(f"❌ Flask import error: {e}")
    print("Install Flask with: pip install flask flask-cors")
    exit(1)

app = Flask(__name__)
CORS(app)

@app.route('/healths', methods=['GET'])
def health_check():
    """Simple health check"""
    return jsonify({
        'status': 'healthy',
        'message': 'Simple Flask API is running'
    })

@app.route('/test-upload', methods=['POST'])
def test_upload():
    """Test upload endpoint"""
    if 'file' not in request.files:
        return jsonify({
            'success': False,
            'message': 'No file provided'
        }), 400
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({
            'success': False,
            'message': 'No file selected'
        }), 400
    
    return jsonify({
        'success': True,
        'message': 'File received successfully',
        'filename': file.filename,
        'size': len(file.read())
    })

@app.route('/test-query', methods=['POST'])
def test_query():
    """Test query endpoint"""
    data = request.get_json()
    
    if not data or 'question' not in data:
        return jsonify({
            'success': False,
            'message': 'No question provided'
        }), 400
    
    question = data['question']
    
    return jsonify({
        'success': True,
        'question': question,
        'answer': f"Test response to: {question}"
    })

if __name__ == '__main__':
    print("🚀 Starting Simple Flask Test API...")
    print("📍 URL: http://localhost:5000")
    print("🔍 Test endpoints:")
    print("   GET  /health")
    print("   POST /test-upload")
    print("   POST /test-query")
    print("=" * 50)
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=True)
    except Exception as e:
        print(f"❌ Failed to start Flask: {e}")
        print("Common solutions:")
        print("1. Check if port 5000 is already in use")
        print("2. Try a different port: app.run(port=5001)")
        print("3. Check firewall settings")
