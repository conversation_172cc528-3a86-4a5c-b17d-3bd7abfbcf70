<?php
/**
 * Python Bridge for WP RAG Chatbot
 * Executes Python scripts directly from PHP
 *
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * WP RAG Chatbot Python Bridge Class
 *
 * @since 1.0.0
 */
class WP_RAG_Chatbot_Python_Bridge {

    /**
     * Python script directory
     *
     * @var string
     * @since 1.0.0
     */
    private $python_dir;

    /**
     * Constructor
     *
     * @since 1.0.0
     */
    public function __construct() {
        $this->python_dir = WP_RAG_CHATBOT_PLUGIN_PATH . 'python/';
        
        // Ensure Python directory exists
        if (!file_exists($this->python_dir)) {
            wp_mkdir_p($this->python_dir);
        }
    }

    /**
     * Process PDF file using Python
     *
     * @param string $file_path Path to PDF file
     * @param string $filename Original filename
     * @return array Result array
     * @since 1.0.0
     */
    public function process_pdf($file_path, $filename) {
        try {
            // Prepare arguments for Python script
            $args = array(
                'action' => 'process_pdf',
                'file_path' => $file_path,
                'filename' => $filename,
                'openai_key' => $this->get_api_key('openai'),
                'pinecone_key' => $this->get_api_key('pinecone')
            );

            // Execute Python script
            $result = $this->execute_python_script('pdf_processor.py', $args);
            
            if ($result['success']) {
                return array(
                    'success' => true,
                    'message' => __('PDF processed successfully.', 'wp-rag-chatbot'),
                    'pdf_hash' => $result['data']['pdf_hash'] ?? '',
                    'chunks_count' => $result['data']['chunks_count'] ?? 0
                );
            } else {
                return array(
                    'success' => false,
                    'message' => $result['error'] ?? __('PDF processing failed.', 'wp-rag-chatbot')
                );
            }

        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => sprintf(__('Error processing PDF: %s', 'wp-rag-chatbot'), $e->getMessage())
            );
        }
    }

    /**
     * Query the RAG system using Python
     *
     * @param string $question User question
     * @return array Result array
     * @since 1.0.0
     */
    public function query_rag($question) {
        try {
            // Prepare arguments for Python script
            $args = array(
                'action' => 'query_rag',
                'question' => $question,
                'openai_key' => $this->get_api_key('openai'),
                'pinecone_key' => $this->get_api_key('pinecone')
            );

            // Execute Python script
            $result = $this->execute_python_script('rag_query.py', $args);
            
            if ($result['success']) {
                return array(
                    'success' => true,
                    'question' => $question,
                    'answer' => $result['data']['answer'] ?? __('No answer available.', 'wp-rag-chatbot')
                );
            } else {
                return array(
                    'success' => false,
                    'message' => $result['error'] ?? __('Query failed.', 'wp-rag-chatbot')
                );
            }

        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => sprintf(__('Error querying RAG: %s', 'wp-rag-chatbot'), $e->getMessage())
            );
        }
    }

    /**
     * Execute Python script with arguments
     *
     * @param string $script_name Python script filename
     * @param array $args Arguments to pass to script
     * @return array Result array
     * @since 1.0.0
     */
    private function execute_python_script($script_name, $args) {
        $script_path = $this->python_dir . $script_name;
        
        if (!file_exists($script_path)) {
            return array(
                'success' => false,
                'error' => sprintf(__('Python script not found: %s', 'wp-rag-chatbot'), $script_name)
            );
        }

        // Create temporary JSON file with arguments
        $temp_file = tempnam(sys_get_temp_dir(), 'wp_rag_');
        file_put_contents($temp_file, json_encode($args));

        // Build Python command with proper path
        $python_cmd = $this->get_python_command();

        // Add Picone directory to Python path
        $picone_path = dirname(WP_RAG_CHATBOT_PLUGIN_PATH);
        $python_path_cmd = sprintf(
            'set PYTHONPATH=%s;%%PYTHONPATH%% && %s "%s" "%s" 2>&1',
            $picone_path,
            $python_cmd,
            $script_path,
            $temp_file
        );

        $command = $python_path_cmd;

        // Execute command
        $output = array();
        $return_code = 0;
        exec($command, $output, $return_code);

        // Clean up temp file
        unlink($temp_file);

        // Parse output
        $output_string = implode("\n", $output);
        
        if ($return_code === 0) {
            // Try to parse JSON output
            $decoded = json_decode($output_string, true);
            if ($decoded !== null) {
                return array(
                    'success' => true,
                    'data' => $decoded
                );
            } else {
                return array(
                    'success' => true,
                    'data' => array('output' => $output_string)
                );
            }
        } else {
            return array(
                'success' => false,
                'error' => $output_string
            );
        }
    }

    /**
     * Get Python command based on system
     *
     * @return string Python command
     * @since 1.0.0
     */
    private function get_python_command() {
        // Try different Python commands
        $python_commands = array('python3', 'python', 'py');
        
        foreach ($python_commands as $cmd) {
            $test_cmd = sprintf('%s --version 2>&1', $cmd);
            $output = array();
            $return_code = 0;
            exec($test_cmd, $output, $return_code);
            
            if ($return_code === 0) {
                return $cmd;
            }
        }
        
        // Default to python
        return 'python';
    }

    /**
     * Get API key from settings
     *
     * @param string $provider API provider (openai, pinecone)
     * @return string API key
     * @since 1.0.0
     */
    private function get_api_key($provider) {
        $settings = get_option('wp_rag_chatbot_settings', array());
        
        switch ($provider) {
            case 'openai':
                return $settings['openai_api_key'] ?? '';
            case 'pinecone':
                return $settings['pinecone_api_key'] ?? '';
            default:
                return '';
        }
    }

    /**
     * Test Python environment
     *
     * @return array Test result
     * @since 1.0.0
     */
    public function test_python_environment() {
        try {
            // Test basic Python execution
            $python_cmd = $this->get_python_command();
            $command = sprintf('%s --version 2>&1', $python_cmd);
            
            $output = array();
            $return_code = 0;
            exec($command, $output, $return_code);
            
            if ($return_code !== 0) {
                return array(
                    'success' => false,
                    'message' => __('Python is not installed or not accessible.', 'wp-rag-chatbot')
                );
            }
            
            $python_version = implode(' ', $output);
            
            // Test required packages
            $test_script = $this->python_dir . 'test_environment.py';
            if (file_exists($test_script)) {
                $result = $this->execute_python_script('test_environment.py', array());
                
                if ($result['success']) {
                    return array(
                        'success' => true,
                        'message' => sprintf(__('Python environment ready. %s', 'wp-rag-chatbot'), $python_version),
                        'details' => $result['data']
                    );
                } else {
                    return array(
                        'success' => false,
                        'message' => sprintf(__('Python packages missing: %s', 'wp-rag-chatbot'), $result['error'])
                    );
                }
            }
            
            return array(
                'success' => true,
                'message' => sprintf(__('Python available: %s', 'wp-rag-chatbot'), $python_version)
            );
            
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => sprintf(__('Python test failed: %s', 'wp-rag-chatbot'), $e->getMessage())
            );
        }
    }

    /**
     * Install Python dependencies using the install script
     *
     * @return array Installation result
     * @since 1.0.0
     */
    public function install_dependencies() {
        try {
            $install_script = $this->python_dir . 'install_requirements.py';

            if (!file_exists($install_script)) {
                return array(
                    'success' => false,
                    'message' => 'Installation script not found.'
                );
            }

            // Execute installation script
            $result = $this->execute_python_script('install_requirements.py', array());

            if ($result['success']) {
                return array(
                    'success' => true,
                    'message' => 'Package installation completed.',
                    'details' => $result['data']
                );
            } else {
                return array(
                    'success' => false,
                    'message' => 'Package installation failed: ' . $result['error']
                );
            }

        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => 'Installation error: ' . $e->getMessage()
            );
        }
    }
}
